Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) for DMA1_Stream0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler) for DMA1_Stream1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART5_IRQHandler) for UART5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    uart.o(i.uart2_deinit) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) for HAL_UART_DeInit
    uart.o(i.uart2_deinit) refers to uart.o(.bss) for .bss
    uart.o(i.uart2_flush) refers to uart.o(.data) for .data
    uart.o(i.uart2_get_handle) refers to uart.o(.bss) for .bss
    uart.o(i.uart2_init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    uart.o(i.uart2_init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    uart.o(i.uart2_init) refers to uart.o(.bss) for .bss
    uart.o(i.uart2_init) refers to uart.o(.data) for .data
    uart.o(i.uart2_irq_handler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    uart.o(i.uart2_irq_handler) refers to uart.o(.data) for .data
    uart.o(i.uart2_irq_handler) refers to uart.o(.bss) for .bss
    uart.o(i.uart2_read) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    uart.o(i.uart2_read) refers to memcpya.o(.text) for __aeabi_memcpy
    uart.o(i.uart2_read) refers to uart.o(.data) for .data
    uart.o(i.uart2_read) refers to uart.o(.bss) for .bss
    uart.o(i.uart2_set_tx_done) refers to uart.o(.data) for .data
    uart.o(i.uart2_write) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT) for HAL_UART_Transmit_IT
    uart.o(i.uart2_write) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    uart.o(i.uart2_write) refers to uart.o(.data) for .data
    uart.o(i.uart2_write) refers to uart.o(.bss) for .bss
    uart.o(i.uart_deinit) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) for HAL_UART_DeInit
    uart.o(i.uart_deinit) refers to uart.o(.bss) for .bss
    uart.o(i.uart_flush) refers to uart.o(.data) for .data
    uart.o(i.uart_get_handle) refers to uart.o(.bss) for .bss
    uart.o(i.uart_init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    uart.o(i.uart_init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    uart.o(i.uart_init) refers to uart.o(.bss) for .bss
    uart.o(i.uart_init) refers to uart.o(.data) for .data
    uart.o(i.uart_irq_handler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    uart.o(i.uart_irq_handler) refers to uart.o(.data) for .data
    uart.o(i.uart_irq_handler) refers to uart.o(.bss) for .bss
    uart.o(i.uart_print) refers to memseta.o(.text) for __aeabi_memclr4
    uart.o(i.uart_print) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart.o(i.uart_print) refers to strlen.o(.text) for strlen
    uart.o(i.uart_print) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT) for HAL_UART_Transmit_IT
    uart.o(i.uart_print) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    uart.o(i.uart_print) refers to uart.o(.data) for .data
    uart.o(i.uart_print) refers to uart.o(.bss) for .bss
    uart.o(i.uart_read) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    uart.o(i.uart_read) refers to memcpya.o(.text) for __aeabi_memcpy
    uart.o(i.uart_read) refers to uart.o(.data) for .data
    uart.o(i.uart_read) refers to uart.o(.bss) for .bss
    uart.o(i.uart_set_tx_done) refers to uart.o(.data) for .data
    uart.o(i.uart_write) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT) for HAL_UART_Transmit_IT
    uart.o(i.uart_write) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    uart.o(i.uart_write) refers to uart.o(.data) for .data
    uart.o(i.uart_write) refers to uart.o(.bss) for .bss
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.bsp_get_systick) refers to stm32f4xx_hal.o(.data) for uwTick
    main.o(i.fputc) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    main.o(i.fputc) refers to usart.o(.bss) for huart1
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_UART5_Init) for MX_UART5_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to spi.o(i.MX_SPI1_Init) for MX_SPI1_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to multitimer.o(i.multiTimerInstall) for multiTimerInstall
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    main.o(i.main) refers to app_oled.o(i.ssd1306_basic_init) for ssd1306_basic_init
    main.o(i.main) refers to app_oled.o(i.ssd1306_basic_clear) for ssd1306_basic_clear
    main.o(i.main) refers to app_motor.o(i.Motor_Init) for Motor_Init
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) for Emm_V5_Reset_CurPos_To_Zero
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to app_uasrt.o(i.save_initial_position) for save_initial_position
    main.o(i.main) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    main.o(i.main) refers to multitimer.o(i.multiTimerYield) for multiTimerYield
    main.o(i.main) refers to main.o(i.bsp_get_systick) for bsp_get_systick
    main.o(i.main) refers to app_uasrt.o(.bss) for ringbuffer_pool_y
    main.o(i.main) refers to app_uasrt.o(.bss) for ringbuffer_y
    main.o(i.main) refers to app_uasrt.o(.bss) for ringbuffer_pool_x
    main.o(i.main) refers to app_uasrt.o(.bss) for ringbuffer_pool_cam
    main.o(i.main) refers to app_uasrt.o(.bss) for ringbuffer_cam
    main.o(i.main) refers to app_uasrt.o(.bss) for ringbuffer_pool_user
    main.o(i.main) refers to usart.o(.bss) for huart5
    main.o(i.main) refers to main.o(i.oled_task) for oled_task
    main.o(i.main) refers to main.o(.bss) for .bss
    main.o(i.oled_task) refers to app_oled.o(i.ssd1306_basic_clear) for ssd1306_basic_clear
    main.o(i.oled_task) refers to printfa.o(i.__0sprintf) for __2sprintf
    main.o(i.oled_task) refers to strlen.o(.text) for strlen
    main.o(i.oled_task) refers to app_oled.o(i.ssd1306_basic_string) for ssd1306_basic_string
    main.o(i.oled_task) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    main.o(i.oled_task) refers to main.o(.data) for .data
    main.o(i.oled_task) refers to main.o(.bss) for .bss
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    spi.o(i.spi_deinit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    spi.o(i.spi_deinit) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit) for HAL_SPI_DeInit
    spi.o(i.spi_deinit) refers to spi.o(.bss) for .bss
    spi.o(i.spi_init) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.spi_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(i.spi_init) refers to spi.o(.bss) for .bss
    spi.o(i.spi_read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    spi.o(i.spi_read) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    spi.o(i.spi_read) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) for HAL_SPI_Receive
    spi.o(i.spi_read) refers to spi.o(.bss) for .bss
    spi.o(i.spi_read_address16) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    spi.o(i.spi_read_address16) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    spi.o(i.spi_read_address16) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) for HAL_SPI_Receive
    spi.o(i.spi_read_address16) refers to spi.o(.bss) for .bss
    spi.o(i.spi_read_cmd) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    spi.o(i.spi_read_cmd) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) for HAL_SPI_Receive
    spi.o(i.spi_read_cmd) refers to spi.o(.bss) for .bss
    spi.o(i.spi_transmit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    spi.o(i.spi_transmit) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    spi.o(i.spi_transmit) refers to spi.o(.bss) for .bss
    spi.o(i.spi_write) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    spi.o(i.spi_write) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    spi.o(i.spi_write) refers to spi.o(.bss) for .bss
    spi.o(i.spi_write_address16) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    spi.o(i.spi_write_address16) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    spi.o(i.spi_write_address16) refers to spi.o(.bss) for .bss
    spi.o(i.spi_write_cmd) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    spi.o(i.spi_write_cmd) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    spi.o(i.spi_write_cmd) refers to spi.o(.bss) for .bss
    spi.o(i.spi_write_read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    spi.o(i.spi_write_read) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    spi.o(i.spi_write_read) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) for HAL_SPI_Receive
    spi.o(i.spi_write_read) refers to spi.o(.bss) for .bss
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to app_uasrt.o(i.my_printf) for my_printf
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to app_uasrt.o(i.usart_task) for usart_task
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for .bss
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to app_uasrt.o(.bss) for ringbuffer_y
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to main.o(.bss) for mt_usart
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to app_uasrt.o(i.user_task) for user_task
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to main.o(.bss) for mt_user
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to app_uasrt.o(.bss) for ringbuffer_cam
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to app_maixcam.o(i.maixcam_task) for maixcam_task
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to main.o(.bss) for mt_cam
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART5_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_UART5_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to usart.o(.bss) for hdma_uart5_rx
    stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler) refers to usart.o(.bss) for hdma_usart3_rx
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to usart.o(.bss) for hdma_usart2_rx
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to usart.o(.bss) for huart5
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    stm32f4xx_hal_msp.o(i.HAL_SPI_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32f4xx_hal_msp.o(i.HAL_SPI_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit) refers to stm32f4xx_hal_msp.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Init) refers to stm32f4xx_hal_msp.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    delay.o(i.delay_init) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig) for HAL_SYSTICK_CLKSourceConfig
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    iic.o(i.a_iic_read_byte) refers to delay.o(i.delay_us) for delay_us
    iic.o(i.a_iic_send_byte) refers to delay.o(i.delay_us) for delay_us
    iic.o(i.a_iic_start) refers to delay.o(i.delay_us) for delay_us
    iic.o(i.a_iic_stop) refers to delay.o(i.delay_us) for delay_us
    iic.o(i.a_iic_wait_ack) refers to delay.o(i.delay_us) for delay_us
    iic.o(i.a_iic_wait_ack) refers to iic.o(i.a_iic_stop) for a_iic_stop
    iic.o(i.iic_deinit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    iic.o(i.iic_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    iic.o(i.iic_read) refers to iic.o(i.a_iic_start) for a_iic_start
    iic.o(i.iic_read) refers to iic.o(i.a_iic_send_byte) for a_iic_send_byte
    iic.o(i.iic_read) refers to iic.o(i.a_iic_wait_ack) for a_iic_wait_ack
    iic.o(i.iic_read) refers to iic.o(i.a_iic_stop) for a_iic_stop
    iic.o(i.iic_read) refers to iic.o(i.a_iic_read_byte) for a_iic_read_byte
    iic.o(i.iic_read_address16) refers to iic.o(i.a_iic_start) for a_iic_start
    iic.o(i.iic_read_address16) refers to iic.o(i.a_iic_send_byte) for a_iic_send_byte
    iic.o(i.iic_read_address16) refers to iic.o(i.a_iic_wait_ack) for a_iic_wait_ack
    iic.o(i.iic_read_address16) refers to iic.o(i.a_iic_stop) for a_iic_stop
    iic.o(i.iic_read_address16) refers to iic.o(i.a_iic_read_byte) for a_iic_read_byte
    iic.o(i.iic_read_cmd) refers to iic.o(i.a_iic_start) for a_iic_start
    iic.o(i.iic_read_cmd) refers to iic.o(i.a_iic_send_byte) for a_iic_send_byte
    iic.o(i.iic_read_cmd) refers to iic.o(i.a_iic_wait_ack) for a_iic_wait_ack
    iic.o(i.iic_read_cmd) refers to iic.o(i.a_iic_stop) for a_iic_stop
    iic.o(i.iic_read_cmd) refers to iic.o(i.a_iic_read_byte) for a_iic_read_byte
    iic.o(i.iic_write) refers to iic.o(i.a_iic_start) for a_iic_start
    iic.o(i.iic_write) refers to iic.o(i.a_iic_send_byte) for a_iic_send_byte
    iic.o(i.iic_write) refers to iic.o(i.a_iic_wait_ack) for a_iic_wait_ack
    iic.o(i.iic_write) refers to iic.o(i.a_iic_stop) for a_iic_stop
    iic.o(i.iic_write_address16) refers to iic.o(i.a_iic_start) for a_iic_start
    iic.o(i.iic_write_address16) refers to iic.o(i.a_iic_send_byte) for a_iic_send_byte
    iic.o(i.iic_write_address16) refers to iic.o(i.a_iic_wait_ack) for a_iic_wait_ack
    iic.o(i.iic_write_address16) refers to iic.o(i.a_iic_stop) for a_iic_stop
    iic.o(i.iic_write_cmd) refers to iic.o(i.a_iic_start) for a_iic_start
    iic.o(i.iic_write_cmd) refers to iic.o(i.a_iic_send_byte) for a_iic_send_byte
    iic.o(i.iic_write_cmd) refers to iic.o(i.a_iic_wait_ack) for a_iic_wait_ack
    iic.o(i.iic_write_cmd) refers to iic.o(i.a_iic_stop) for a_iic_stop
    wire.o(i.wire_clock_deinit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    wire.o(i.wire_clock_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    wire.o(i.wire_clock_write) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    wire.o(i.wire_deinit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    wire.o(i.wire_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    driver_ssd1306.o(i.ssd1306_activate_scroll) refers to driver_ssd1306.o(i.a_ssd1306_write_byte) for a_ssd1306_write_byte
    driver_ssd1306.o(i.ssd1306_clear) refers to driver_ssd1306.o(i.a_ssd1306_write_byte) for a_ssd1306_write_byte
    driver_ssd1306.o(i.ssd1306_deactivate_scroll) refers to driver_ssd1306.o(i.a_ssd1306_write_byte) for a_ssd1306_write_byte
    driver_ssd1306.o(i.ssd1306_deinit) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_deinit) refers to driver_ssd1306.o(i.a_ssd1306_write_byte) for a_ssd1306_write_byte
    driver_ssd1306.o(i.ssd1306_gram_update) refers to driver_ssd1306.o(i.a_ssd1306_write_byte) for a_ssd1306_write_byte
    driver_ssd1306.o(i.ssd1306_gram_write_string) refers to driver_ssd1306.o(.constdata) for .constdata
    driver_ssd1306.o(i.ssd1306_info) refers to memseta.o(.text) for __aeabi_memclr4
    driver_ssd1306.o(i.ssd1306_info) refers to strncpy.o(.text) for strncpy
    driver_ssd1306.o(i.ssd1306_set_charge_pump) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_column_address_range) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_com_pins_hardware_conf) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_contrast) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_deselect_level) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_display) refers to driver_ssd1306.o(i.a_ssd1306_write_byte) for a_ssd1306_write_byte
    driver_ssd1306.o(i.ssd1306_set_display_clock) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_display_mode) refers to driver_ssd1306.o(i.a_ssd1306_write_byte) for a_ssd1306_write_byte
    driver_ssd1306.o(i.ssd1306_set_display_offset) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_display_start_line) refers to driver_ssd1306.o(i.a_ssd1306_write_byte) for a_ssd1306_write_byte
    driver_ssd1306.o(i.ssd1306_set_entire_display) refers to driver_ssd1306.o(i.a_ssd1306_write_byte) for a_ssd1306_write_byte
    driver_ssd1306.o(i.ssd1306_set_fade_blinking_mode) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_high_column_start_address) refers to driver_ssd1306.o(i.a_ssd1306_write_byte) for a_ssd1306_write_byte
    driver_ssd1306.o(i.ssd1306_set_left_horizontal_scroll) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_low_column_start_address) refers to driver_ssd1306.o(i.a_ssd1306_write_byte) for a_ssd1306_write_byte
    driver_ssd1306.o(i.ssd1306_set_memory_addressing_mode) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_multiplex_ratio) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_page_address) refers to driver_ssd1306.o(i.a_ssd1306_write_byte) for a_ssd1306_write_byte
    driver_ssd1306.o(i.ssd1306_set_page_address_range) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_precharge_period) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_right_horizontal_scroll) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_scan_direction) refers to driver_ssd1306.o(i.a_ssd1306_write_byte) for a_ssd1306_write_byte
    driver_ssd1306.o(i.ssd1306_set_segment_remap) refers to driver_ssd1306.o(i.a_ssd1306_write_byte) for a_ssd1306_write_byte
    driver_ssd1306.o(i.ssd1306_set_vertical_left_horizontal_scroll) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_vertical_right_horizontal_scroll) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_vertical_scroll_area) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_set_zoom_in) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_write_cmd) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_write_data) refers to driver_ssd1306.o(i.a_ssd1306_multiple_write_byte) for a_ssd1306_multiple_write_byte
    driver_ssd1306.o(i.ssd1306_write_point) refers to driver_ssd1306.o(i.a_ssd1306_write_byte) for a_ssd1306_write_byte
    stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_delay_ms) refers to delay.o(i.delay_ms) for delay_ms
    stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_deinit) refers to iic.o(i.iic_deinit) for iic_deinit
    stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_init) refers to iic.o(i.iic_init) for iic_init
    stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_write) refers to iic.o(i.iic_write) for iic_write
    stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_deinit) refers to wire.o(i.wire_clock_deinit) for wire_clock_deinit
    stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_init) refers to wire.o(i.wire_clock_init) for wire_clock_init
    stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_write) refers to wire.o(i.wire_clock_write) for wire_clock_write
    stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_deinit) refers to wire.o(i.wire_deinit) for wire_deinit
    stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_init) refers to wire.o(i.wire_init) for wire_init
    stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_write) refers to wire.o(i.wire_write) for wire_write
    stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_deinit) refers to spi.o(i.spi_deinit) for spi_deinit
    stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_init) refers to spi.o(i.spi_init) for spi_init
    stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_write_cmd) refers to spi.o(i.spi_write_cmd) for spi_write_cmd
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    multitimer.o(i.multiTimerInstall) refers to multitimer.o(.data) for .data
    multitimer.o(i.multiTimerStart) refers to multitimer.o(.data) for .data
    multitimer.o(i.multiTimerStop) refers to multitimer.o(.data) for .data
    multitimer.o(i.multiTimerYield) refers to multitimer.o(.data) for .data
    emm_v5.o(i.Emm_V5_En_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Interrupt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Set_O) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Parse_Response) refers to memseta.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Pos_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Stop_Now) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Synchronous_motion) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Vel_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    app_oled.o(i.ssd1306_basic_clear) refers to driver_ssd1306.o(i.ssd1306_clear) for ssd1306_clear
    app_oled.o(i.ssd1306_basic_clear) refers to app_oled.o(.bss) for .bss
    app_oled.o(i.ssd1306_basic_deinit) refers to driver_ssd1306.o(i.ssd1306_deinit) for ssd1306_deinit
    app_oled.o(i.ssd1306_basic_deinit) refers to app_oled.o(.bss) for .bss
    app_oled.o(i.ssd1306_basic_display_off) refers to driver_ssd1306.o(i.ssd1306_set_display) for ssd1306_set_display
    app_oled.o(i.ssd1306_basic_display_off) refers to app_oled.o(.bss) for .bss
    app_oled.o(i.ssd1306_basic_display_on) refers to driver_ssd1306.o(i.ssd1306_set_display) for ssd1306_set_display
    app_oled.o(i.ssd1306_basic_display_on) refers to app_oled.o(.bss) for .bss
    app_oled.o(i.ssd1306_basic_init) refers to memseta.o(.text) for __aeabi_memclr4
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_interface) for ssd1306_set_interface
    app_oled.o(i.ssd1306_basic_init) refers to stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_debug_print) for ssd1306_interface_debug_print
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_addr_pin) for ssd1306_set_addr_pin
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_init) for ssd1306_init
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_display) for ssd1306_set_display
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_column_address_range) for ssd1306_set_column_address_range
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_deinit) for ssd1306_deinit
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_page_address_range) for ssd1306_set_page_address_range
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_low_column_start_address) for ssd1306_set_low_column_start_address
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_high_column_start_address) for ssd1306_set_high_column_start_address
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_display_start_line) for ssd1306_set_display_start_line
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_fade_blinking_mode) for ssd1306_set_fade_blinking_mode
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_deactivate_scroll) for ssd1306_deactivate_scroll
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_zoom_in) for ssd1306_set_zoom_in
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_contrast) for ssd1306_set_contrast
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_segment_remap) for ssd1306_set_segment_remap
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_scan_direction) for ssd1306_set_scan_direction
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_display_mode) for ssd1306_set_display_mode
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_multiplex_ratio) for ssd1306_set_multiplex_ratio
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_display_offset) for ssd1306_set_display_offset
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_display_clock) for ssd1306_set_display_clock
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_precharge_period) for ssd1306_set_precharge_period
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_com_pins_hardware_conf) for ssd1306_set_com_pins_hardware_conf
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_deselect_level) for ssd1306_set_deselect_level
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_memory_addressing_mode) for ssd1306_set_memory_addressing_mode
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_charge_pump) for ssd1306_set_charge_pump
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_set_entire_display) for ssd1306_set_entire_display
    app_oled.o(i.ssd1306_basic_init) refers to app_oled.o(.bss) for .bss
    app_oled.o(i.ssd1306_basic_init) refers to stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_init) for ssd1306_interface_iic_init
    app_oled.o(i.ssd1306_basic_init) refers to stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_deinit) for ssd1306_interface_iic_deinit
    app_oled.o(i.ssd1306_basic_init) refers to stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_write) for ssd1306_interface_iic_write
    app_oled.o(i.ssd1306_basic_init) refers to stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_init) for ssd1306_interface_spi_init
    app_oled.o(i.ssd1306_basic_init) refers to stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_deinit) for ssd1306_interface_spi_deinit
    app_oled.o(i.ssd1306_basic_init) refers to stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_write_cmd) for ssd1306_interface_spi_write_cmd
    app_oled.o(i.ssd1306_basic_init) refers to stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_init) for ssd1306_interface_spi_cmd_data_gpio_init
    app_oled.o(i.ssd1306_basic_init) refers to stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_deinit) for ssd1306_interface_spi_cmd_data_gpio_deinit
    app_oled.o(i.ssd1306_basic_init) refers to stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_write) for ssd1306_interface_spi_cmd_data_gpio_write
    app_oled.o(i.ssd1306_basic_init) refers to stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_init) for ssd1306_interface_reset_gpio_init
    app_oled.o(i.ssd1306_basic_init) refers to stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_deinit) for ssd1306_interface_reset_gpio_deinit
    app_oled.o(i.ssd1306_basic_init) refers to stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_write) for ssd1306_interface_reset_gpio_write
    app_oled.o(i.ssd1306_basic_init) refers to stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_delay_ms) for ssd1306_interface_delay_ms
    app_oled.o(i.ssd1306_basic_init) refers to driver_ssd1306.o(i.ssd1306_clear) for ssd1306_clear
    app_oled.o(i.ssd1306_basic_picture) refers to driver_ssd1306.o(i.ssd1306_gram_draw_picture) for ssd1306_gram_draw_picture
    app_oled.o(i.ssd1306_basic_picture) refers to driver_ssd1306.o(i.ssd1306_gram_update) for ssd1306_gram_update
    app_oled.o(i.ssd1306_basic_picture) refers to app_oled.o(.bss) for .bss
    app_oled.o(i.ssd1306_basic_read_point) refers to driver_ssd1306.o(i.ssd1306_read_point) for ssd1306_read_point
    app_oled.o(i.ssd1306_basic_read_point) refers to app_oled.o(.bss) for .bss
    app_oled.o(i.ssd1306_basic_rect) refers to driver_ssd1306.o(i.ssd1306_gram_fill_rect) for ssd1306_gram_fill_rect
    app_oled.o(i.ssd1306_basic_rect) refers to driver_ssd1306.o(i.ssd1306_gram_update) for ssd1306_gram_update
    app_oled.o(i.ssd1306_basic_rect) refers to app_oled.o(.bss) for .bss
    app_oled.o(i.ssd1306_basic_string) refers to driver_ssd1306.o(i.ssd1306_gram_write_string) for ssd1306_gram_write_string
    app_oled.o(i.ssd1306_basic_string) refers to driver_ssd1306.o(i.ssd1306_gram_update) for ssd1306_gram_update
    app_oled.o(i.ssd1306_basic_string) refers to app_oled.o(.bss) for .bss
    app_oled.o(i.ssd1306_basic_write_point) refers to driver_ssd1306.o(i.ssd1306_write_point) for ssd1306_write_point
    app_oled.o(i.ssd1306_basic_write_point) refers to app_oled.o(.bss) for .bss
    app_motor.o(i.Motor_Init) refers to emm_v5.o(i.Emm_V5_En_Control) for Emm_V5_En_Control
    app_motor.o(i.Motor_Init) refers to emm_v5.o(i.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    app_motor.o(i.Motor_Init) refers to usart.o(.bss) for huart2
    app_motor.o(i.Motor_Set_Speed) refers to emm_v5.o(i.Emm_V5_Vel_Control) for Emm_V5_Vel_Control
    app_motor.o(i.Motor_Set_Speed) refers to usart.o(.bss) for huart2
    app_motor.o(i.Motor_Stop) refers to emm_v5.o(i.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    app_motor.o(i.Motor_Stop) refers to usart.o(.bss) for huart2
    app_uasrt.o(i.check_motor_angle_limits) refers to emm_v5.o(i.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    app_uasrt.o(i.check_motor_angle_limits) refers to app_uasrt.o(.data) for .data
    app_uasrt.o(i.check_motor_angle_limits) refers to usart.o(.bss) for huart2
    app_uasrt.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    app_uasrt.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    app_uasrt.o(i.parse_x_motor_data) refers to app_uasrt.o(i.my_printf) for my_printf
    app_uasrt.o(i.parse_x_motor_data) refers to app_uasrt.o(.data) for .data
    app_uasrt.o(i.parse_x_motor_data) refers to usart.o(.bss) for huart1
    app_uasrt.o(i.parse_y_motor_data) refers to app_uasrt.o(i.my_printf) for my_printf
    app_uasrt.o(i.parse_y_motor_data) refers to app_uasrt.o(.data) for .data
    app_uasrt.o(i.parse_y_motor_data) refers to app_uasrt.o(i.parse_x_motor_data) for i.parse_x_motor_data
    app_uasrt.o(i.parse_y_motor_data) refers to usart.o(.bss) for huart1
    app_uasrt.o(i.process_command) refers to _scanf_int.o(.text) for _scanf_int
    app_uasrt.o(i.process_command) refers to strncmp.o(.text) for strncmp
    app_uasrt.o(i.process_command) refers to __0sscanf.o(.text) for __0sscanf
    app_uasrt.o(i.process_command) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    app_uasrt.o(i.process_command) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    app_uasrt.o(i.process_command) refers to app_uasrt.o(i.my_printf) for my_printf
    app_uasrt.o(i.process_command) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    app_uasrt.o(i.process_command) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_uasrt.o(i.process_command) refers to usart.o(.bss) for huart1
    app_uasrt.o(i.process_command) refers to app_uasrt.o(.data) for .data
    app_uasrt.o(i.process_command) refers to app_uasrt.o(i.process_reset_command) for i.process_reset_command
    app_uasrt.o(i.process_reset_command) refers to app_uasrt.o(i.my_printf) for my_printf
    app_uasrt.o(i.process_reset_command) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    app_uasrt.o(i.process_reset_command) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    app_uasrt.o(i.process_reset_command) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    app_uasrt.o(i.process_reset_command) refers to app_uasrt.o(.data) for .data
    app_uasrt.o(i.process_reset_command) refers to usart.o(.bss) for huart1
    app_uasrt.o(i.save_initial_position) refers to emm_v5.o(i.Emm_V5_Read_Sys_Params) for Emm_V5_Read_Sys_Params
    app_uasrt.o(i.save_initial_position) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    app_uasrt.o(i.save_initial_position) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    app_uasrt.o(i.save_initial_position) refers to app_uasrt.o(.data) for .data
    app_uasrt.o(i.save_initial_position) refers to usart.o(.bss) for huart2
    app_uasrt.o(i.usart_task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    app_uasrt.o(i.usart_task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    app_uasrt.o(i.usart_task) refers to emm_v5.o(i.Emm_V5_Parse_Response) for Emm_V5_Parse_Response
    app_uasrt.o(i.usart_task) refers to app_uasrt.o(i.parse_x_motor_data) for parse_x_motor_data
    app_uasrt.o(i.usart_task) refers to memseta.o(.text) for __aeabi_memclr4
    app_uasrt.o(i.usart_task) refers to app_uasrt.o(i.parse_y_motor_data) for parse_y_motor_data
    app_uasrt.o(i.usart_task) refers to emm_v5.o(i.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    app_uasrt.o(i.usart_task) refers to app_uasrt.o(.bss) for .bss
    app_uasrt.o(i.usart_task) refers to app_uasrt.o(.data) for .data
    app_uasrt.o(i.usart_task) refers to usart.o(.bss) for huart2
    app_uasrt.o(i.user_task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    app_uasrt.o(i.user_task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    app_uasrt.o(i.user_task) refers to app_uasrt.o(i.process_command) for process_command
    app_uasrt.o(i.user_task) refers to app_pid.o(i.app_pid_parse_cmd) for app_pid_parse_cmd
    app_uasrt.o(i.user_task) refers to memseta.o(.text) for __aeabi_memclr4
    app_uasrt.o(i.user_task) refers to app_uasrt.o(.bss) for .bss
    app_maixcam.o(i.default_laser_callback) refers to app_uasrt.o(i.my_printf) for my_printf
    app_maixcam.o(i.default_laser_callback) refers to app_pid.o(i.app_pid_init) for app_pid_init
    app_maixcam.o(i.default_laser_callback) refers to app_pid.o(i.app_pid_set_target) for app_pid_set_target
    app_maixcam.o(i.default_laser_callback) refers to app_pid.o(i.app_pid_start) for app_pid_start
    app_maixcam.o(i.default_laser_callback) refers to usart.o(.bss) for huart1
    app_maixcam.o(i.maixcam_parse_data) refers to _scanf_int.o(.text) for _scanf_int
    app_maixcam.o(i.maixcam_parse_data) refers to strncmp.o(.text) for strncmp
    app_maixcam.o(i.maixcam_parse_data) refers to __0sscanf.o(.text) for __0sscanf
    app_maixcam.o(i.maixcam_parse_data) refers to app_maixcam.o(.data) for .data
    app_maixcam.o(i.maixcam_set_callback) refers to app_maixcam.o(.data) for .data
    app_maixcam.o(i.maixcam_set_callback) refers to app_maixcam.o(i.default_laser_callback) for default_laser_callback
    app_maixcam.o(i.maixcam_task) refers to _scanf_int.o(.text) for _scanf_int
    app_maixcam.o(i.maixcam_task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    app_maixcam.o(i.maixcam_task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    app_maixcam.o(i.maixcam_task) refers to strncmp.o(.text) for strncmp
    app_maixcam.o(i.maixcam_task) refers to __0sscanf.o(.text) for __0sscanf
    app_maixcam.o(i.maixcam_task) refers to memseta.o(.text) for __aeabi_memclr
    app_maixcam.o(i.maixcam_task) refers to app_uasrt.o(.bss) for ringbuffer_cam
    app_maixcam.o(i.maixcam_task) refers to app_uasrt.o(.bss) for output_buffer_cam
    app_maixcam.o(i.maixcam_task) refers to app_maixcam.o(i.maixcam_parse_data) for i.maixcam_parse_data
    app_maixcam.o(i.maixcam_task) refers to app_maixcam.o(.data) for .data
    app_maixcam.o(.data) refers to app_maixcam.o(i.default_laser_callback) for default_laser_callback
    app_pid.o(i.app_pid_calc) refers to app_motor.o(i.Motor_Stop) for Motor_Stop
    app_pid.o(i.app_pid_calc) refers to pid.o(i.pid_calculate_positional) for pid_calculate_positional
    app_pid.o(i.app_pid_calc) refers to app_motor.o(i.Motor_Set_Speed) for Motor_Set_Speed
    app_pid.o(i.app_pid_calc) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_calc) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_init) refers to pid.o(i.pid_init) for pid_init
    app_pid.o(i.app_pid_init) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_pid.o(i.app_pid_init) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_init) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_init) refers to app_pid.o(i.app_pid_report_task) for app_pid_report_task
    app_pid.o(i.app_pid_parse_cmd) refers to _scanf_int.o(.text) for _scanf_int
    app_pid.o(i.app_pid_parse_cmd) refers to __0sscanf.o(.text) for __0sscanf
    app_pid.o(i.app_pid_parse_cmd) refers to strncmp.o(.text) for strncmp
    app_pid.o(i.app_pid_parse_cmd) refers to pid.o(i.pid_set_target) for pid_set_target
    app_pid.o(i.app_pid_parse_cmd) refers to pid.o(i.pid_set_params) for pid_set_params
    app_pid.o(i.app_pid_parse_cmd) refers to pid.o(i.pid_set_limit) for pid_set_limit
    app_pid.o(i.app_pid_parse_cmd) refers to app_maixcam.o(i.maixcam_set_callback) for maixcam_set_callback
    app_pid.o(i.app_pid_parse_cmd) refers to pid.o(i.pid_reset) for pid_reset
    app_pid.o(i.app_pid_parse_cmd) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_pid.o(i.app_pid_parse_cmd) refers to app_motor.o(i.Motor_Stop) for Motor_Stop
    app_pid.o(i.app_pid_parse_cmd) refers to multitimer.o(i.multiTimerStop) for multiTimerStop
    app_pid.o(i.app_pid_parse_cmd) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_parse_cmd) refers to app_pid.o(i.app_pid_task) for app_pid_task
    app_pid.o(i.app_pid_parse_cmd) refers to app_pid.o(i.app_pid_report_task) for app_pid_report_task
    app_pid.o(i.app_pid_parse_cmd) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_parse_cmd) refers to app_pid.o(i.pid_laser_coord_callback) for pid_laser_coord_callback
    app_pid.o(i.app_pid_report) refers to app_uasrt.o(i.my_printf) for my_printf
    app_pid.o(i.app_pid_report) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_report) refers to usart.o(.bss) for huart1
    app_pid.o(i.app_pid_report_task) refers to app_uasrt.o(i.my_printf) for my_printf
    app_pid.o(i.app_pid_report_task) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_pid.o(i.app_pid_report_task) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_report_task) refers to app_pid.o(i.app_pid_report) for i.app_pid_report
    app_pid.o(i.app_pid_report_task) refers to usart.o(.bss) for huart1
    app_pid.o(i.app_pid_report_task) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_set_target) refers to pid.o(i.pid_set_target) for pid_set_target
    app_pid.o(i.app_pid_set_target) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_set_target) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_set_x_params) refers to pid.o(i.pid_set_params) for pid_set_params
    app_pid.o(i.app_pid_set_x_params) refers to pid.o(i.pid_set_limit) for pid_set_limit
    app_pid.o(i.app_pid_set_x_params) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_set_x_params) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_set_y_params) refers to pid.o(i.pid_set_params) for pid_set_params
    app_pid.o(i.app_pid_set_y_params) refers to pid.o(i.pid_set_limit) for pid_set_limit
    app_pid.o(i.app_pid_set_y_params) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_set_y_params) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_start) refers to app_maixcam.o(i.maixcam_set_callback) for maixcam_set_callback
    app_pid.o(i.app_pid_start) refers to pid.o(i.pid_reset) for pid_reset
    app_pid.o(i.app_pid_start) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_pid.o(i.app_pid_start) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_start) refers to app_pid.o(i.pid_laser_coord_callback) for pid_laser_coord_callback
    app_pid.o(i.app_pid_start) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_start) refers to app_pid.o(i.app_pid_task) for app_pid_task
    app_pid.o(i.app_pid_start) refers to app_pid.o(i.app_pid_report_task) for app_pid_report_task
    app_pid.o(i.app_pid_stop) refers to app_motor.o(i.Motor_Stop) for Motor_Stop
    app_pid.o(i.app_pid_stop) refers to multitimer.o(i.multiTimerStop) for multiTimerStop
    app_pid.o(i.app_pid_stop) refers to app_maixcam.o(i.maixcam_set_callback) for maixcam_set_callback
    app_pid.o(i.app_pid_stop) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_stop) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_task) refers to app_pid.o(i.app_pid_calc) for app_pid_calc
    app_pid.o(i.app_pid_task) refers to multitimer.o(i.multiTimerStart) for multiTimerStart
    app_pid.o(i.app_pid_task) refers to app_pid.o(.data) for .data
    app_pid.o(i.app_pid_task) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.app_pid_update_position) refers to app_pid.o(.data) for .data
    app_pid.o(i.pid_laser_coord_callback) refers to pid.o(i.pid_set_target) for pid_set_target
    app_pid.o(i.pid_laser_coord_callback) refers to app_pid.o(.data) for .data
    app_pid.o(i.pid_laser_coord_callback) refers to app_pid.o(.bss) for .bss
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_c.o(.text) for isspace
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (512 bytes).
    Removing uart.o(.rev16_text), (4 bytes).
    Removing uart.o(.revsh_text), (4 bytes).
    Removing uart.o(.rrx_text), (6 bytes).
    Removing uart.o(i.uart2_deinit), (20 bytes).
    Removing uart.o(i.uart2_flush), (12 bytes).
    Removing uart.o(i.uart2_get_handle), (8 bytes).
    Removing uart.o(i.uart2_init), (68 bytes).
    Removing uart.o(i.uart2_irq_handler), (56 bytes).
    Removing uart.o(i.uart2_read), (60 bytes).
    Removing uart.o(i.uart2_set_tx_done), (12 bytes).
    Removing uart.o(i.uart2_write), (68 bytes).
    Removing uart.o(i.uart_deinit), (20 bytes).
    Removing uart.o(i.uart_flush), (12 bytes).
    Removing uart.o(i.uart_get_handle), (8 bytes).
    Removing uart.o(i.uart_init), (68 bytes).
    Removing uart.o(i.uart_irq_handler), (48 bytes).
    Removing uart.o(i.uart_print), (112 bytes).
    Removing uart.o(i.uart_read), (60 bytes).
    Removing uart.o(i.uart_set_tx_done), (12 bytes).
    Removing uart.o(i.uart_write), (68 bytes).
    Removing uart.o(.bss), (912 bytes).
    Removing uart.o(.data), (8 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.fputc), (28 bytes).
    Removing main.o(.bss), (24 bytes).
    Removing main.o(.bss), (24 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.spi_read), (96 bytes).
    Removing spi.o(i.spi_read_address16), (100 bytes).
    Removing spi.o(i.spi_read_cmd), (72 bytes).
    Removing spi.o(i.spi_transmit), (80 bytes).
    Removing spi.o(i.spi_write), (96 bytes).
    Removing spi.o(i.spi_write_address16), (100 bytes).
    Removing spi.o(i.spi_write_read), (100 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (216 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort), (316 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT), (312 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAPause), (38 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAResume), (38 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop), (66 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler), (268 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive), (358 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (244 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT), (336 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive), (562 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (288 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (184 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (212 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (148 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (50 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (50 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (50 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (50 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR), (92 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR), (28 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (148 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError), (16 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt), (108 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback), (104 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt), (102 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (92 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback), (120 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction), (122 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT), (104 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT), (104 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT), (156 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT), (156 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (216 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (148 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (60 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (124 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (56 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (100 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (368 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (356 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (292 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (236 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (52 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (76 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (336 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (196 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (68 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (212 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (36 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (124 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2056 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (140 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (474 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (84 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (108 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (152 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (32 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (92 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (64 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (64 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (30 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (86 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (30 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (136 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (176 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (40 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (200 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (64 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (64 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (282 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (224 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (158 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (160 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (108 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (256 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (122 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (212 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (12 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (354 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (32 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT), (82 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (58 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (48 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (48 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT), (54 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (132 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing iic.o(.rev16_text), (4 bytes).
    Removing iic.o(.revsh_text), (4 bytes).
    Removing iic.o(.rrx_text), (6 bytes).
    Removing iic.o(i.a_iic_read_byte), (184 bytes).
    Removing iic.o(i.iic_read), (114 bytes).
    Removing iic.o(i.iic_read_address16), (126 bytes).
    Removing iic.o(i.iic_read_cmd), (78 bytes).
    Removing iic.o(i.iic_write_address16), (98 bytes).
    Removing iic.o(i.iic_write_cmd), (66 bytes).
    Removing wire.o(.rev16_text), (4 bytes).
    Removing wire.o(.revsh_text), (4 bytes).
    Removing wire.o(.rrx_text), (6 bytes).
    Removing wire.o(i.wire_read), (36 bytes).
    Removing driver_ssd1306.o(i.ssd1306_activate_scroll), (28 bytes).
    Removing driver_ssd1306.o(i.ssd1306_get_addr_pin), (18 bytes).
    Removing driver_ssd1306.o(i.ssd1306_get_interface), (18 bytes).
    Removing driver_ssd1306.o(i.ssd1306_gram_draw_picture), (300 bytes).
    Removing driver_ssd1306.o(i.ssd1306_gram_fill_rect), (292 bytes).
    Removing driver_ssd1306.o(i.ssd1306_gram_read_point), (116 bytes).
    Removing driver_ssd1306.o(i.ssd1306_gram_write_point), (120 bytes).
    Removing driver_ssd1306.o(i.ssd1306_info), (172 bytes).
    Removing driver_ssd1306.o(i.ssd1306_read_point), (116 bytes).
    Removing driver_ssd1306.o(i.ssd1306_set_left_horizontal_scroll), (204 bytes).
    Removing driver_ssd1306.o(i.ssd1306_set_page_address), (84 bytes).
    Removing driver_ssd1306.o(i.ssd1306_set_right_horizontal_scroll), (204 bytes).
    Removing driver_ssd1306.o(i.ssd1306_set_vertical_left_horizontal_scroll), (240 bytes).
    Removing driver_ssd1306.o(i.ssd1306_set_vertical_right_horizontal_scroll), (240 bytes).
    Removing driver_ssd1306.o(i.ssd1306_set_vertical_scroll_area), (188 bytes).
    Removing driver_ssd1306.o(i.ssd1306_write_cmd), (26 bytes).
    Removing driver_ssd1306.o(i.ssd1306_write_data), (26 bytes).
    Removing driver_ssd1306.o(i.ssd1306_write_point), (232 bytes).
    Removing stm32f407_driver_ssd1306_interface.o(.rev16_text), (4 bytes).
    Removing stm32f407_driver_ssd1306_interface.o(.revsh_text), (4 bytes).
    Removing stm32f407_driver_ssd1306_interface.o(.rrx_text), (6 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (76 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (78 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (176 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (80 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (128 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (16 bytes).
    Removing emm_v5.o(.rev16_text), (4 bytes).
    Removing emm_v5.o(.revsh_text), (4 bytes).
    Removing emm_v5.o(.rrx_text), (6 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode), (70 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Interrupt), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Modify_Params), (182 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Set_O), (56 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Trigger_Return), (64 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_Clog_Pro), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Synchronous_motion), (52 bytes).
    Removing pid.o(i.pid_calculate_incremental), (156 bytes).
    Removing app_oled.o(.rev16_text), (4 bytes).
    Removing app_oled.o(.revsh_text), (4 bytes).
    Removing app_oled.o(.rrx_text), (6 bytes).
    Removing app_oled.o(i.ssd1306_basic_deinit), (20 bytes).
    Removing app_oled.o(i.ssd1306_basic_display_off), (24 bytes).
    Removing app_oled.o(i.ssd1306_basic_display_on), (24 bytes).
    Removing app_oled.o(i.ssd1306_basic_picture), (52 bytes).
    Removing app_oled.o(i.ssd1306_basic_read_point), (28 bytes).
    Removing app_oled.o(i.ssd1306_basic_rect), (52 bytes).
    Removing app_oled.o(i.ssd1306_basic_write_point), (28 bytes).
    Removing app_motor.o(.rev16_text), (4 bytes).
    Removing app_motor.o(.revsh_text), (4 bytes).
    Removing app_motor.o(.rrx_text), (6 bytes).
    Removing app_uasrt.o(.rev16_text), (4 bytes).
    Removing app_uasrt.o(.revsh_text), (4 bytes).
    Removing app_uasrt.o(.rrx_text), (6 bytes).
    Removing app_uasrt.o(i.calc_motor_angle), (44 bytes).
    Removing app_uasrt.o(i.calc_relative_angle), (68 bytes).
    Removing app_uasrt.o(i.check_motor_angle_limits), (124 bytes).
    Removing app_maixcam.o(.rev16_text), (4 bytes).
    Removing app_maixcam.o(.revsh_text), (4 bytes).
    Removing app_maixcam.o(.rrx_text), (6 bytes).
    Removing app_pid.o(.rev16_text), (4 bytes).
    Removing app_pid.o(.revsh_text), (4 bytes).
    Removing app_pid.o(.rrx_text), (6 bytes).
    Removing app_pid.o(i.app_pid_parse_target), (20 bytes).
    Removing app_pid.o(i.app_pid_set_x_params), (88 bytes).
    Removing app_pid.o(i.app_pid_set_y_params), (88 bytes).
    Removing app_pid.o(i.app_pid_stop), (52 bytes).
    Removing app_pid.o(i.app_pid_update_position), (32 bytes).

397 unused section(s) (total 26912 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/stdlib/abort.c          0x00000000   Number         0  abort.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/string/strncpy.c        0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\App\app_maixcam.c                     0x00000000   Number         0  app_maixcam.o ABSOLUTE
    ..\App\app_motor.c                       0x00000000   Number         0  app_motor.o ABSOLUTE
    ..\App\app_oled.c                        0x00000000   Number         0  app_oled.o ABSOLUTE
    ..\App\app_pid.c                         0x00000000   Number         0  app_pid.o ABSOLUTE
    ..\App\app_uasrt.c                       0x00000000   Number         0  app_uasrt.o ABSOLUTE
    ..\Components\interface\src\delay.c      0x00000000   Number         0  delay.o ABSOLUTE
    ..\Components\interface\src\iic.c        0x00000000   Number         0  iic.o ABSOLUTE
    ..\Components\interface\src\uart.c       0x00000000   Number         0  uart.o ABSOLUTE
    ..\Components\interface\src\wire.c       0x00000000   Number         0  wire.o ABSOLUTE
    ..\Components\motor\Emm_V5.c             0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\Components\multi_timer\MultiTimer.c   0x00000000   Number         0  multitimer.o ABSOLUTE
    ..\Components\pid\pid.c                  0x00000000   Number         0  pid.o ABSOLUTE
    ..\Components\ringbuffer\ringbuffer.c    0x00000000   Number         0  ringbuffer.o ABSOLUTE
    ..\Components\ssd1106\interface\stm32f407_driver_ssd1306_interface.c 0x00000000   Number         0  stm32f407_driver_ssd1306_interface.o ABSOLUTE
    ..\Components\ssd1106\src\driver_ssd1306.c 0x00000000   Number         0  driver_ssd1306.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\\App\\app_maixcam.c                   0x00000000   Number         0  app_maixcam.o ABSOLUTE
    ..\\App\\app_motor.c                     0x00000000   Number         0  app_motor.o ABSOLUTE
    ..\\App\\app_oled.c                      0x00000000   Number         0  app_oled.o ABSOLUTE
    ..\\App\\app_pid.c                       0x00000000   Number         0  app_pid.o ABSOLUTE
    ..\\App\\app_uasrt.c                     0x00000000   Number         0  app_uasrt.o ABSOLUTE
    ..\\Components\\interface\\src\\delay.c  0x00000000   Number         0  delay.o ABSOLUTE
    ..\\Components\\interface\\src\\iic.c    0x00000000   Number         0  iic.o ABSOLUTE
    ..\\Components\\interface\\src\\uart.c   0x00000000   Number         0  uart.o ABSOLUTE
    ..\\Components\\interface\\src\\wire.c   0x00000000   Number         0  wire.o ABSOLUTE
    ..\\Components\\motor\\Emm_V5.c          0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\\Components\\ssd1106\\interface\\stm32f407_driver_ssd1306_interface.c 0x00000000   Number         0  stm32f407_driver_ssd1306_interface.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000198   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0800019c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0800019c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0800019c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0800019c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001a0   Section       36  startup_stm32f407xx.o(.text)
    $v0                                      0x080001a0   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080001c4   Section        0  uldiv.o(.text)
    .text                                    0x08000226   Section        0  memcpya.o(.text)
    .text                                    0x0800024a   Section        0  memseta.o(.text)
    .text                                    0x0800026e   Section        0  strlen.o(.text)
    .text                                    0x0800027c   Section        0  strncmp.o(.text)
    .text                                    0x0800029c   Section        0  __0sscanf.o(.text)
    .text                                    0x080002d4   Section        0  _scanf_int.o(.text)
    .text                                    0x08000420   Section        0  uidiv.o(.text)
    .text                                    0x0800044c   Section        0  llshl.o(.text)
    .text                                    0x0800046a   Section        0  llushr.o(.text)
    .text                                    0x0800048a   Section        0  _chval.o(.text)
    .text                                    0x080004a8   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x080004a9   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x080004d0   Section        0  _sgetc.o(.text)
    .text                                    0x08000510   Section        0  dadd.o(.text)
    .text                                    0x08000510   Section        0  iusefp.o(.text)
    .text                                    0x0800065e   Section        0  dmul.o(.text)
    .text                                    0x08000742   Section        0  ddiv.o(.text)
    .text                                    0x08000820   Section        0  dfixul.o(.text)
    .text                                    0x08000850   Section       48  cdrcmple.o(.text)
    .text                                    0x08000880   Section       36  init.o(.text)
    .text                                    0x080008a4   Section        0  llsshr.o(.text)
    .text                                    0x080008c8   Section        0  isspace_c.o(.text)
    .text                                    0x080008d4   Section        0  _scanf.o(.text)
    .text                                    0x08000c04   Section        0  depilogue.o(.text)
    .text                                    0x08000cc0   Section        0  ctype_c.o(.text)
    i.BusFault_Handler                       0x08000ce8   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA1_Stream0_IRQHandler                0x08000cec   Section        0  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    i.DMA1_Stream1_IRQHandler                0x08000cf8   Section        0  stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler)
    i.DMA1_Stream5_IRQHandler                0x08000d04   Section        0  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    i.DMA2_Stream2_IRQHandler                0x08000d10   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DebugMon_Handler                       0x08000d1c   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Emm_V5_En_Control                      0x08000d1e   Section        0  emm_v5.o(i.Emm_V5_En_Control)
    i.Emm_V5_Parse_Response                  0x08000d64   Section        0  emm_v5.o(i.Emm_V5_Parse_Response)
    i.Emm_V5_Pos_Control                     0x08000fea   Section        0  emm_v5.o(i.Emm_V5_Pos_Control)
    i.Emm_V5_Read_Sys_Params                 0x08001056   Section        0  emm_v5.o(i.Emm_V5_Read_Sys_Params)
    i.Emm_V5_Reset_CurPos_To_Zero            0x08001130   Section        0  emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero)
    i.Emm_V5_Stop_Now                        0x08001164   Section        0  emm_v5.o(i.Emm_V5_Stop_Now)
    i.Emm_V5_Vel_Control                     0x0800119c   Section        0  emm_v5.o(i.Emm_V5_Vel_Control)
    i.Error_Handler                          0x080011ee   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x080011f2   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08001298   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x080012bc   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08001498   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x080015f4   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08001688   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_DeInit                        0x080016b0   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit)
    i.HAL_GPIO_Init                          0x08001800   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x08001a00   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08001a0c   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08001a18   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08001a28   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08001a5c   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08001a9c   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08001ad0   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08001af0   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08001b54   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08001b78   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08001cd4   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08001cf4   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08001d14   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08001d88   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SPI_DeInit                         0x0800218c   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit)
    i.HAL_SPI_Init                           0x080021bc   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspDeInit                      0x08002280   Section        0  stm32f4xx_hal_msp.o(i.HAL_SPI_MspDeInit)
    i.HAL_SPI_MspInit                        0x080022a8   Section        0  stm32f4xx_hal_msp.o(i.HAL_SPI_MspInit)
    i.HAL_SPI_Transmit                       0x0800230c   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit)
    i.HAL_SYSTICK_CLKSourceConfig            0x080024ac   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    i.HAL_SYSTICK_Config                     0x080024c4   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x080024f0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x08002550   Section        0  usart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x08002710   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08002714   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08002a1c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08002a80   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x08002cfc   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08002cfe   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x08002d00   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08002e52   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08002e54   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MX_DMA_Init                            0x08002e58   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08002ec4   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_SPI1_Init                           0x08002f9c   Section        0  spi.o(i.MX_SPI1_Init)
    i.MX_UART5_Init                          0x08002fa0   Section        0  usart.o(i.MX_UART5_Init)
    i.MX_USART1_UART_Init                    0x08002ff4   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08003048   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x0800309c   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MemManage_Handler                      0x080030f0   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.Motor_Init                             0x080030f4   Section        0  app_motor.o(i.Motor_Init)
    i.Motor_Set_Speed                        0x08003130   Section        0  app_motor.o(i.Motor_Set_Speed)
    i.Motor_Stop                             0x080031cc   Section        0  app_motor.o(i.Motor_Stop)
    i.NMI_Handler                            0x080031f0   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x080031f2   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SPI_EndRxTxTransaction                 0x080031f4   Section        0  stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    SPI_EndRxTxTransaction                   0x080031f5   Thumb Code    98  stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    i.SPI_WaitFlagStateUntilTimeout          0x08003260   Section        0  stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    SPI_WaitFlagStateUntilTimeout            0x08003261   Thumb Code   204  stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    i.SVC_Handler                            0x08003330   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08003332   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08003338   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x080033cc   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.UART5_IRQHandler                       0x080033dc   Section        0  stm32f4xx_it.o(i.UART5_IRQHandler)
    i.UART_DMAAbortOnError                   0x08003408   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08003409   Thumb Code    16  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08003418   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08003419   Thumb Code   174  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x080034c6   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x080034c7   Thumb Code   152  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x0800355e   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x0800355f   Thumb Code    30  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_Receive_IT                        0x0800357c   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x0800357d   Thumb Code   206  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x0800364c   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x0800364d   Thumb Code   236  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x0800373c   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_WaitOnFlagUntilTimeout            0x080037dc   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x080037dd   Thumb Code   186  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08003898   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x080038c4   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x080038f0   Section        0  stm32f4xx_it.o(i.USART3_IRQHandler)
    i.UsageFault_Handler                     0x0800391c   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__0sprintf                             0x08003920   Section        0  printfa.o(i.__0sprintf)
    i.__0vsnprintf                           0x08003948   Section        0  printfa.o(i.__0vsnprintf)
    i.__scatterload_copy                     0x0800397c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800398a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800398c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x0800399c   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x0800399d   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08003b20   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08003b21   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x080041d4   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x080041d5   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x080041f8   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x080041f9   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x08004226   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08004227   Thumb Code    22  printfa.o(i._snputc)
    i._sputc                                 0x0800423c   Section        0  printfa.o(i._sputc)
    _sputc                                   0x0800423d   Thumb Code    10  printfa.o(i._sputc)
    i.a_iic_send_byte                        0x08004248   Section        0  iic.o(i.a_iic_send_byte)
    a_iic_send_byte                          0x08004249   Thumb Code    86  iic.o(i.a_iic_send_byte)
    i.a_iic_start                            0x080042a8   Section        0  iic.o(i.a_iic_start)
    a_iic_start                              0x080042a9   Thumb Code    56  iic.o(i.a_iic_start)
    i.a_iic_stop                             0x080042e8   Section        0  iic.o(i.a_iic_stop)
    a_iic_stop                               0x080042e9   Thumb Code    64  iic.o(i.a_iic_stop)
    i.a_iic_wait_ack                         0x08004330   Section        0  iic.o(i.a_iic_wait_ack)
    a_iic_wait_ack                           0x08004331   Thumb Code    76  iic.o(i.a_iic_wait_ack)
    i.a_ssd1306_multiple_write_byte          0x08004384   Section        0  driver_ssd1306.o(i.a_ssd1306_multiple_write_byte)
    a_ssd1306_multiple_write_byte            0x08004385   Thumb Code    82  driver_ssd1306.o(i.a_ssd1306_multiple_write_byte)
    i.a_ssd1306_write_byte                   0x080043d6   Section        0  driver_ssd1306.o(i.a_ssd1306_write_byte)
    a_ssd1306_write_byte                     0x080043d7   Thumb Code    88  driver_ssd1306.o(i.a_ssd1306_write_byte)
    i.app_pid_calc                           0x08004430   Section        0  app_pid.o(i.app_pid_calc)
    i.app_pid_init                           0x08004584   Section        0  app_pid.o(i.app_pid_init)
    i.app_pid_parse_cmd                      0x080045f8   Section        0  app_pid.o(i.app_pid_parse_cmd)
    i.app_pid_report                         0x0800486c   Section        0  app_pid.o(i.app_pid_report)
    i.app_pid_report_task                    0x080048e0   Section        0  app_pid.o(i.app_pid_report_task)
    i.app_pid_set_target                     0x08004954   Section        0  app_pid.o(i.app_pid_set_target)
    i.app_pid_start                          0x08004988   Section        0  app_pid.o(i.app_pid_start)
    i.app_pid_task                           0x08004a10   Section        0  app_pid.o(i.app_pid_task)
    i.bsp_get_systick                        0x08004a60   Section        0  main.o(i.bsp_get_systick)
    i.default_laser_callback                 0x08004a6c   Section        0  app_maixcam.o(i.default_laser_callback)
    default_laser_callback                   0x08004a6d   Thumb Code    80  app_maixcam.o(i.default_laser_callback)
    i.delay_init                             0x08004b0c   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x08004b24   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x08004b28   Section        0  delay.o(i.delay_us)
    i.iic_deinit                             0x08004b5c   Section        0  iic.o(i.iic_deinit)
    i.iic_init                               0x08004b70   Section        0  iic.o(i.iic_init)
    i.iic_write                              0x08004bbc   Section        0  iic.o(i.iic_write)
    i.main                                   0x08004c14   Section        0  main.o(i.main)
    i.maixcam_parse_data                     0x08004ce4   Section        0  app_maixcam.o(i.maixcam_parse_data)
    i.maixcam_set_callback                   0x08004d80   Section        0  app_maixcam.o(i.maixcam_set_callback)
    i.maixcam_task                           0x08004d94   Section        0  app_maixcam.o(i.maixcam_task)
    i.multiTimerInstall                      0x08004e40   Section        0  multitimer.o(i.multiTimerInstall)
    i.multiTimerStart                        0x08004e58   Section        0  multitimer.o(i.multiTimerStart)
    i.multiTimerStop                         0x08004ec8   Section        0  multitimer.o(i.multiTimerStop)
    i.multiTimerYield                        0x08004eec   Section        0  multitimer.o(i.multiTimerYield)
    i.my_printf                              0x08004f30   Section        0  app_uasrt.o(i.my_printf)
    i.oled_task                              0x08004f64   Section        0  main.o(i.oled_task)
    i.parse_x_motor_data                     0x08004fcc   Section        0  app_uasrt.o(i.parse_x_motor_data)
    i.parse_y_motor_data                     0x080050c0   Section        0  app_uasrt.o(i.parse_y_motor_data)
    i.pid_calculate_positional               0x08005198   Section        0  pid.o(i.pid_calculate_positional)
    i.pid_init                               0x08005220   Section        0  pid.o(i.pid_init)
    i.pid_laser_coord_callback               0x08005250   Section        0  app_pid.o(i.pid_laser_coord_callback)
    i.pid_reset                              0x080052ac   Section        0  pid.o(i.pid_reset)
    i.pid_set_limit                          0x080052d4   Section        0  pid.o(i.pid_set_limit)
    i.pid_set_params                         0x080052da   Section        0  pid.o(i.pid_set_params)
    i.pid_set_target                         0x080052e8   Section        0  pid.o(i.pid_set_target)
    i.process_command                        0x080052f0   Section        0  app_uasrt.o(i.process_command)
    i.process_reset_command                  0x0800541c   Section        0  app_uasrt.o(i.process_reset_command)
    i.rt_ringbuffer_data_len                 0x080054e0   Section        0  ringbuffer.o(i.rt_ringbuffer_data_len)
    i.rt_ringbuffer_get                      0x08005510   Section        0  ringbuffer.o(i.rt_ringbuffer_get)
    i.rt_ringbuffer_init                     0x0800558e   Section        0  ringbuffer.o(i.rt_ringbuffer_init)
    i.rt_ringbuffer_put                      0x080055c2   Section        0  ringbuffer.o(i.rt_ringbuffer_put)
    i.save_initial_position                  0x08005644   Section        0  app_uasrt.o(i.save_initial_position)
    i.spi_deinit                             0x080056b4   Section        0  spi.o(i.spi_deinit)
    i.spi_init                               0x080056d4   Section        0  spi.o(i.spi_init)
    i.spi_write_cmd                          0x0800576c   Section        0  spi.o(i.spi_write_cmd)
    i.ssd1306_basic_clear                    0x080057b4   Section        0  app_oled.o(i.ssd1306_basic_clear)
    i.ssd1306_basic_init                     0x080057c8   Section        0  app_oled.o(i.ssd1306_basic_init)
    i.ssd1306_basic_string                   0x08005ee0   Section        0  app_oled.o(i.ssd1306_basic_string)
    i.ssd1306_clear                          0x08005f18   Section        0  driver_ssd1306.o(i.ssd1306_clear)
    i.ssd1306_deactivate_scroll              0x08005fc0   Section        0  driver_ssd1306.o(i.ssd1306_deactivate_scroll)
    i.ssd1306_deinit                         0x08005fdc   Section        0  driver_ssd1306.o(i.ssd1306_deinit)
    i.ssd1306_gram_update                    0x08006150   Section        0  driver_ssd1306.o(i.ssd1306_gram_update)
    i.ssd1306_gram_write_string              0x080061f0   Section        0  driver_ssd1306.o(i.ssd1306_gram_write_string)
    i.ssd1306_init                           0x080063d4   Section        0  driver_ssd1306.o(i.ssd1306_init)
    i.ssd1306_interface_debug_print          0x080067e0   Section        0  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_debug_print)
    i.ssd1306_interface_delay_ms             0x080067e2   Section        0  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_delay_ms)
    i.ssd1306_interface_iic_deinit           0x080067e6   Section        0  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_deinit)
    i.ssd1306_interface_iic_init             0x080067ea   Section        0  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_init)
    i.ssd1306_interface_iic_write            0x080067ee   Section        0  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_write)
    i.ssd1306_interface_reset_gpio_deinit    0x080067f2   Section        0  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_deinit)
    i.ssd1306_interface_reset_gpio_init      0x080067f6   Section        0  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_init)
    i.ssd1306_interface_reset_gpio_write     0x080067fa   Section        0  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_write)
    i.ssd1306_interface_spi_cmd_data_gpio_deinit 0x080067fe   Section        0  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_deinit)
    i.ssd1306_interface_spi_cmd_data_gpio_init 0x08006802   Section        0  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_init)
    i.ssd1306_interface_spi_cmd_data_gpio_write 0x08006806   Section        0  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_write)
    i.ssd1306_interface_spi_deinit           0x0800680a   Section        0  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_deinit)
    i.ssd1306_interface_spi_init             0x0800680e   Section        0  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_init)
    i.ssd1306_interface_spi_write_cmd        0x08006814   Section        0  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_write_cmd)
    i.ssd1306_set_addr_pin                   0x08006818   Section        0  driver_ssd1306.o(i.ssd1306_set_addr_pin)
    i.ssd1306_set_charge_pump                0x08006826   Section        0  driver_ssd1306.o(i.ssd1306_set_charge_pump)
    i.ssd1306_set_column_address_range       0x08006858   Section        0  driver_ssd1306.o(i.ssd1306_set_column_address_range)
    i.ssd1306_set_com_pins_hardware_conf     0x080068f0   Section        0  driver_ssd1306.o(i.ssd1306_set_com_pins_hardware_conf)
    i.ssd1306_set_contrast                   0x08006926   Section        0  driver_ssd1306.o(i.ssd1306_set_contrast)
    i.ssd1306_set_deselect_level             0x08006952   Section        0  driver_ssd1306.o(i.ssd1306_set_deselect_level)
    i.ssd1306_set_display                    0x08006980   Section        0  driver_ssd1306.o(i.ssd1306_set_display)
    i.ssd1306_set_display_clock              0x080069a8   Section        0  driver_ssd1306.o(i.ssd1306_set_display_clock)
    i.ssd1306_set_display_mode               0x08006a44   Section        0  driver_ssd1306.o(i.ssd1306_set_display_mode)
    i.ssd1306_set_display_offset             0x08006a6c   Section        0  driver_ssd1306.o(i.ssd1306_set_display_offset)
    i.ssd1306_set_display_start_line         0x08006ac8   Section        0  driver_ssd1306.o(i.ssd1306_set_display_start_line)
    i.ssd1306_set_entire_display             0x08006b1c   Section        0  driver_ssd1306.o(i.ssd1306_set_entire_display)
    i.ssd1306_set_fade_blinking_mode         0x08006b44   Section        0  driver_ssd1306.o(i.ssd1306_set_fade_blinking_mode)
    i.ssd1306_set_high_column_start_address  0x08006ba4   Section        0  driver_ssd1306.o(i.ssd1306_set_high_column_start_address)
    i.ssd1306_set_interface                  0x08006bf8   Section        0  driver_ssd1306.o(i.ssd1306_set_interface)
    i.ssd1306_set_low_column_start_address   0x08006c08   Section        0  driver_ssd1306.o(i.ssd1306_set_low_column_start_address)
    i.ssd1306_set_memory_addressing_mode     0x08006c58   Section        0  driver_ssd1306.o(i.ssd1306_set_memory_addressing_mode)
    i.ssd1306_set_multiplex_ratio            0x08006c84   Section        0  driver_ssd1306.o(i.ssd1306_set_multiplex_ratio)
    i.ssd1306_set_page_address_range         0x08006d14   Section        0  driver_ssd1306.o(i.ssd1306_set_page_address_range)
    i.ssd1306_set_precharge_period           0x08006dac   Section        0  driver_ssd1306.o(i.ssd1306_set_precharge_period)
    i.ssd1306_set_scan_direction             0x08006e40   Section        0  driver_ssd1306.o(i.ssd1306_set_scan_direction)
    i.ssd1306_set_segment_remap              0x08006e66   Section        0  driver_ssd1306.o(i.ssd1306_set_segment_remap)
    i.ssd1306_set_zoom_in                    0x08006e8c   Section        0  driver_ssd1306.o(i.ssd1306_set_zoom_in)
    i.usart_task                             0x08006eb8   Section        0  app_uasrt.o(i.usart_task)
    i.user_task                              0x08006fbc   Section        0  app_uasrt.o(i.user_task)
    i.wire_clock_deinit                      0x08007000   Section        0  wire.o(i.wire_clock_deinit)
    i.wire_clock_init                        0x08007014   Section        0  wire.o(i.wire_clock_init)
    i.wire_clock_write                       0x08007050   Section        0  wire.o(i.wire_clock_write)
    i.wire_deinit                            0x08007074   Section        0  wire.o(i.wire_deinit)
    i.wire_init                              0x08007088   Section        0  wire.o(i.wire_init)
    i.wire_write                             0x080070ec   Section        0  wire.o(i.wire_write)
    .constdata                               0x08007110   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x08007110   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x08007118   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x08007128   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08007130   Section     6080  driver_ssd1306.o(.constdata)
    gsc_ssd1306_ascii_1206                   0x08007130   Data        1140  driver_ssd1306.o(.constdata)
    gsc_ssd1306_ascii_1608                   0x080075a4   Data        1520  driver_ssd1306.o(.constdata)
    gsc_ssd1306_ascii_2412                   0x08007b94   Data        3420  driver_ssd1306.o(.constdata)
    .constdata                               0x080088f0   Section       64  ctype_c.o(.constdata)
    .data                                    0x20000000   Section        4  main.o(.data)
    .data                                    0x20000004   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x20000010   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000014   Section        4  delay.o(.data)
    gs_fac_us                                0x20000014   Data           4  delay.o(.data)
    .data                                    0x20000018   Section        8  multitimer.o(.data)
    timerList                                0x20000018   Data           4  multitimer.o(.data)
    platformTicksFunction                    0x2000001c   Data           4  multitimer.o(.data)
    .data                                    0x20000020   Section       40  app_uasrt.o(.data)
    .data                                    0x20000048   Section        4  app_maixcam.o(.data)
    laser_coord_callback                     0x20000048   Data           4  app_maixcam.o(.data)
    .data                                    0x2000004c   Section       92  app_pid.o(.data)
    pid_running                              0x2000004c   Data           1  app_pid.o(.data)
    .bss                                     0x200000a8   Section       44  main.o(.bss)
    .bss                                     0x200000d8   Section       24  main.o(.bss)
    .bss                                     0x200000f0   Section       24  main.o(.bss)
    .bss                                     0x20000108   Section       24  main.o(.bss)
    .bss                                     0x20000120   Section       88  spi.o(.bss)
    .bss                                     0x20000178   Section      928  usart.o(.bss)
    .bss                                     0x20000518   Section     1084  app_oled.o(.bss)
    gs_handle                                0x20000518   Data        1084  app_oled.o(.bss)
    .bss                                     0x20000954   Section       64  app_uasrt.o(.bss)
    .bss                                     0x20000994   Section       64  app_uasrt.o(.bss)
    .bss                                     0x200009d4   Section       64  app_uasrt.o(.bss)
    .bss                                     0x20000a14   Section       64  app_uasrt.o(.bss)
    .bss                                     0x20000a54   Section      228  app_uasrt.o(.bss)
    .bss                                     0x20000b38   Section       64  app_uasrt.o(.bss)
    .bss                                     0x20000b78   Section       12  app_uasrt.o(.bss)
    .bss                                     0x20000b88   Section      168  app_pid.o(.bss)
    mt_pid                                   0x20000c00   Data          24  app_pid.o(.bss)
    mt_pid_report                            0x20000c18   Data          24  app_pid.o(.bss)
    STACK                                    0x20000c30   Section     1024  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_real                               - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000199   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0800019d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0800019d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001a1   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080001c5   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x08000227   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000227   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000227   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0800024b   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800024b   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800024b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000259   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000259   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000259   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800025d   Thumb Code    18  memseta.o(.text)
    strlen                                   0x0800026f   Thumb Code    14  strlen.o(.text)
    strncmp                                  0x0800027d   Thumb Code    30  strncmp.o(.text)
    __0sscanf                                0x0800029d   Thumb Code    48  __0sscanf.o(.text)
    _scanf_int                               0x080002d5   Thumb Code   332  _scanf_int.o(.text)
    __aeabi_uidiv                            0x08000421   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000421   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x0800044d   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800044d   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0800046b   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800046b   Thumb Code     0  llushr.o(.text)
    _chval                                   0x0800048b   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x080004b5   Thumb Code    20  scanf_char.o(.text)
    _sgetc                                   0x080004d1   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x080004ef   Thumb Code    34  _sgetc.o(.text)
    __I$use$fp                               0x08000511   Thumb Code     0  iusefp.o(.text)
    __aeabi_dadd                             0x08000511   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000653   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000659   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x0800065f   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000743   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x08000821   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000851   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000881   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000881   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x080008a5   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080008a5   Thumb Code     0  llsshr.o(.text)
    isspace                                  0x080008c9   Thumb Code    10  isspace_c.o(.text)
    __vfscanf                                0x080008d5   Thumb Code   810  _scanf.o(.text)
    _double_round                            0x08000c05   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000c23   Thumb Code   156  depilogue.o(.text)
    __ctype_lookup                           0x08000cc1   Thumb Code    34  ctype_c.o(.text)
    BusFault_Handler                         0x08000ce9   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DMA1_Stream0_IRQHandler                  0x08000ced   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    DMA1_Stream1_IRQHandler                  0x08000cf9   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler)
    DMA1_Stream5_IRQHandler                  0x08000d05   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x08000d11   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x08000d1d   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Emm_V5_En_Control                        0x08000d1f   Thumb Code    70  emm_v5.o(i.Emm_V5_En_Control)
    Emm_V5_Parse_Response                    0x08000d65   Thumb Code   646  emm_v5.o(i.Emm_V5_Parse_Response)
    Emm_V5_Pos_Control                       0x08000feb   Thumb Code   108  emm_v5.o(i.Emm_V5_Pos_Control)
    Emm_V5_Read_Sys_Params                   0x08001057   Thumb Code   218  emm_v5.o(i.Emm_V5_Read_Sys_Params)
    Emm_V5_Reset_CurPos_To_Zero              0x08001131   Thumb Code    52  emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero)
    Emm_V5_Stop_Now                          0x08001165   Thumb Code    56  emm_v5.o(i.Emm_V5_Stop_Now)
    Emm_V5_Vel_Control                       0x0800119d   Thumb Code    82  emm_v5.o(i.Emm_V5_Vel_Control)
    Error_Handler                            0x080011ef   Thumb Code     4  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x080011f3   Thumb Code   166  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08001299   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x080012bd   Thumb Code   470  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08001499   Thumb Code   334  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x080015f5   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08001689   Thumb Code    34  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_DeInit                          0x080016b1   Thumb Code   324  stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit)
    HAL_GPIO_Init                            0x08001801   Thumb Code   494  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x08001a01   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001a0d   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08001a19   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08001a29   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08001a5d   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08001a9d   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001ad1   Thumb Code    30  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001af1   Thumb Code    96  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001b55   Thumb Code    30  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001b79   Thumb Code   324  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08001cd5   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08001cf5   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08001d15   Thumb Code    98  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001d89   Thumb Code   990  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SPI_DeInit                           0x0800218d   Thumb Code    48  stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit)
    HAL_SPI_Init                             0x080021bd   Thumb Code   194  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspDeInit                        0x08002281   Thumb Code    32  stm32f4xx_hal_msp.o(i.HAL_SPI_MspDeInit)
    HAL_SPI_MspInit                          0x080022a9   Thumb Code    88  stm32f4xx_hal_msp.o(i.HAL_SPI_MspInit)
    HAL_SPI_Transmit                         0x0800230d   Thumb Code   416  stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit)
    HAL_SYSTICK_CLKSourceConfig              0x080024ad   Thumb Code    22  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    HAL_SYSTICK_Config                       0x080024c5   Thumb Code    38  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_UARTEx_ReceiveToIdle_DMA             0x080024f1   Thumb Code    94  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08002551   Thumb Code   382  usart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08002711   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08002715   Thumb Code   772  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08002a1d   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08002a81   Thumb Code   580  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08002cfd   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08002cff   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08002d01   Thumb Code   338  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08002e53   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08002e55   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    MX_DMA_Init                              0x08002e59   Thumb Code   104  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08002ec5   Thumb Code   200  gpio.o(i.MX_GPIO_Init)
    MX_SPI1_Init                             0x08002f9d   Thumb Code     2  spi.o(i.MX_SPI1_Init)
    MX_UART5_Init                            0x08002fa1   Thumb Code    68  usart.o(i.MX_UART5_Init)
    MX_USART1_UART_Init                      0x08002ff5   Thumb Code    68  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08003049   Thumb Code    68  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x0800309d   Thumb Code    68  usart.o(i.MX_USART3_UART_Init)
    MemManage_Handler                        0x080030f1   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    Motor_Init                               0x080030f5   Thumb Code    50  app_motor.o(i.Motor_Init)
    Motor_Set_Speed                          0x08003131   Thumb Code   146  app_motor.o(i.Motor_Set_Speed)
    Motor_Stop                               0x080031cd   Thumb Code    26  app_motor.o(i.Motor_Stop)
    NMI_Handler                              0x080031f1   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x080031f3   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08003331   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08003333   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08003339   Thumb Code   140  main.o(i.SystemClock_Config)
    SystemInit                               0x080033cd   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    UART5_IRQHandler                         0x080033dd   Thumb Code    32  stm32f4xx_it.o(i.UART5_IRQHandler)
    UART_Start_Receive_DMA                   0x0800373d   Thumb Code   148  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    USART1_IRQHandler                        0x08003899   Thumb Code    32  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x080038c5   Thumb Code    32  stm32f4xx_it.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x080038f1   Thumb Code    32  stm32f4xx_it.o(i.USART3_IRQHandler)
    UsageFault_Handler                       0x0800391d   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __0sprintf                               0x08003921   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x08003921   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x08003921   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x08003921   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x08003921   Thumb Code     0  printfa.o(i.__0sprintf)
    __0vsnprintf                             0x08003949   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08003949   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08003949   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08003949   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08003949   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __scatterload_copy                       0x0800397d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800398b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800398d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    app_pid_calc                             0x08004431   Thumb Code   324  app_pid.o(i.app_pid_calc)
    app_pid_init                             0x08004585   Thumb Code    96  app_pid.o(i.app_pid_init)
    app_pid_parse_cmd                        0x080045f9   Thumb Code   518  app_pid.o(i.app_pid_parse_cmd)
    app_pid_report                           0x0800486d   Thumb Code    70  app_pid.o(i.app_pid_report)
    app_pid_report_task                      0x080048e1   Thumb Code    92  app_pid.o(i.app_pid_report_task)
    app_pid_set_target                       0x08004955   Thumb Code    40  app_pid.o(i.app_pid_set_target)
    app_pid_start                            0x08004989   Thumb Code    96  app_pid.o(i.app_pid_start)
    app_pid_task                             0x08004a11   Thumb Code    64  app_pid.o(i.app_pid_task)
    bsp_get_systick                          0x08004a61   Thumb Code     8  main.o(i.bsp_get_systick)
    delay_init                               0x08004b0d   Thumb Code    18  delay.o(i.delay_init)
    delay_ms                                 0x08004b25   Thumb Code     4  delay.o(i.delay_ms)
    delay_us                                 0x08004b29   Thumb Code    46  delay.o(i.delay_us)
    iic_deinit                               0x08004b5d   Thumb Code    16  iic.o(i.iic_deinit)
    iic_init                                 0x08004b71   Thumb Code    62  iic.o(i.iic_init)
    iic_write                                0x08004bbd   Thumb Code    86  iic.o(i.iic_write)
    main                                     0x08004c15   Thumb Code   154  main.o(i.main)
    maixcam_parse_data                       0x08004ce5   Thumb Code   126  app_maixcam.o(i.maixcam_parse_data)
    maixcam_set_callback                     0x08004d81   Thumb Code    12  app_maixcam.o(i.maixcam_set_callback)
    maixcam_task                             0x08004d95   Thumb Code   146  app_maixcam.o(i.maixcam_task)
    multiTimerInstall                        0x08004e41   Thumb Code    18  multitimer.o(i.multiTimerInstall)
    multiTimerStart                          0x08004e59   Thumb Code   108  multitimer.o(i.multiTimerStart)
    multiTimerStop                           0x08004ec9   Thumb Code    30  multitimer.o(i.multiTimerStop)
    multiTimerYield                          0x08004eed   Thumb Code    64  multitimer.o(i.multiTimerYield)
    my_printf                                0x08004f31   Thumb Code    50  app_uasrt.o(i.my_printf)
    oled_task                                0x08004f65   Thumb Code    82  main.o(i.oled_task)
    parse_x_motor_data                       0x08004fcd   Thumb Code   190  app_uasrt.o(i.parse_x_motor_data)
    parse_y_motor_data                       0x080050c1   Thumb Code   190  app_uasrt.o(i.parse_y_motor_data)
    pid_calculate_positional                 0x08005199   Thumb Code   136  pid.o(i.pid_calculate_positional)
    pid_init                                 0x08005221   Thumb Code    42  pid.o(i.pid_init)
    pid_laser_coord_callback                 0x08005251   Thumb Code    78  app_pid.o(i.pid_laser_coord_callback)
    pid_reset                                0x080052ad   Thumb Code    34  pid.o(i.pid_reset)
    pid_set_limit                            0x080052d5   Thumb Code     6  pid.o(i.pid_set_limit)
    pid_set_params                           0x080052db   Thumb Code    14  pid.o(i.pid_set_params)
    pid_set_target                           0x080052e9   Thumb Code     6  pid.o(i.pid_set_target)
    process_command                          0x080052f1   Thumb Code   212  app_uasrt.o(i.process_command)
    process_reset_command                    0x0800541d   Thumb Code   112  app_uasrt.o(i.process_reset_command)
    rt_ringbuffer_data_len                   0x080054e1   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x08005511   Thumb Code   126  ringbuffer.o(i.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x0800558f   Thumb Code    52  ringbuffer.o(i.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x080055c3   Thumb Code   130  ringbuffer.o(i.rt_ringbuffer_put)
    save_initial_position                    0x08005645   Thumb Code    72  app_uasrt.o(i.save_initial_position)
    spi_deinit                               0x080056b5   Thumb Code    24  spi.o(i.spi_deinit)
    spi_init                                 0x080056d5   Thumb Code   134  spi.o(i.spi_init)
    spi_write_cmd                            0x0800576d   Thumb Code    64  spi.o(i.spi_write_cmd)
    ssd1306_basic_clear                      0x080057b5   Thumb Code    16  app_oled.o(i.ssd1306_basic_clear)
    ssd1306_basic_init                       0x080057c9   Thumb Code  1714  app_oled.o(i.ssd1306_basic_init)
    ssd1306_basic_string                     0x08005ee1   Thumb Code    52  app_oled.o(i.ssd1306_basic_string)
    ssd1306_clear                            0x08005f19   Thumb Code   134  driver_ssd1306.o(i.ssd1306_clear)
    ssd1306_deactivate_scroll                0x08005fc1   Thumb Code    28  driver_ssd1306.o(i.ssd1306_deactivate_scroll)
    ssd1306_deinit                           0x08005fdd   Thumb Code   164  driver_ssd1306.o(i.ssd1306_deinit)
    ssd1306_gram_update                      0x08006151   Thumb Code   126  driver_ssd1306.o(i.ssd1306_gram_update)
    ssd1306_gram_write_string                0x080061f1   Thumb Code   448  driver_ssd1306.o(i.ssd1306_gram_write_string)
    ssd1306_init                             0x080063d5   Thumb Code   372  driver_ssd1306.o(i.ssd1306_init)
    ssd1306_interface_debug_print            0x080067e1   Thumb Code     2  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_debug_print)
    ssd1306_interface_delay_ms               0x080067e3   Thumb Code     4  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_delay_ms)
    ssd1306_interface_iic_deinit             0x080067e7   Thumb Code     4  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_deinit)
    ssd1306_interface_iic_init               0x080067eb   Thumb Code     4  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_init)
    ssd1306_interface_iic_write              0x080067ef   Thumb Code     4  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_iic_write)
    ssd1306_interface_reset_gpio_deinit      0x080067f3   Thumb Code     4  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_deinit)
    ssd1306_interface_reset_gpio_init        0x080067f7   Thumb Code     4  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_init)
    ssd1306_interface_reset_gpio_write       0x080067fb   Thumb Code     4  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_reset_gpio_write)
    ssd1306_interface_spi_cmd_data_gpio_deinit 0x080067ff   Thumb Code     4  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_deinit)
    ssd1306_interface_spi_cmd_data_gpio_init 0x08006803   Thumb Code     4  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_init)
    ssd1306_interface_spi_cmd_data_gpio_write 0x08006807   Thumb Code     4  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_cmd_data_gpio_write)
    ssd1306_interface_spi_deinit             0x0800680b   Thumb Code     4  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_deinit)
    ssd1306_interface_spi_init               0x0800680f   Thumb Code     6  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_init)
    ssd1306_interface_spi_write_cmd          0x08006815   Thumb Code     4  stm32f407_driver_ssd1306_interface.o(i.ssd1306_interface_spi_write_cmd)
    ssd1306_set_addr_pin                     0x08006819   Thumb Code    14  driver_ssd1306.o(i.ssd1306_set_addr_pin)
    ssd1306_set_charge_pump                  0x08006827   Thumb Code    50  driver_ssd1306.o(i.ssd1306_set_charge_pump)
    ssd1306_set_column_address_range         0x08006859   Thumb Code    84  driver_ssd1306.o(i.ssd1306_set_column_address_range)
    ssd1306_set_com_pins_hardware_conf       0x080068f1   Thumb Code    54  driver_ssd1306.o(i.ssd1306_set_com_pins_hardware_conf)
    ssd1306_set_contrast                     0x08006927   Thumb Code    44  driver_ssd1306.o(i.ssd1306_set_contrast)
    ssd1306_set_deselect_level               0x08006953   Thumb Code    46  driver_ssd1306.o(i.ssd1306_set_deselect_level)
    ssd1306_set_display                      0x08006981   Thumb Code    38  driver_ssd1306.o(i.ssd1306_set_display)
    ssd1306_set_display_clock                0x080069a9   Thumb Code    76  driver_ssd1306.o(i.ssd1306_set_display_clock)
    ssd1306_set_display_mode                 0x08006a45   Thumb Code    38  driver_ssd1306.o(i.ssd1306_set_display_mode)
    ssd1306_set_display_offset               0x08006a6d   Thumb Code    58  driver_ssd1306.o(i.ssd1306_set_display_offset)
    ssd1306_set_display_start_line           0x08006ac9   Thumb Code    54  driver_ssd1306.o(i.ssd1306_set_display_start_line)
    ssd1306_set_entire_display               0x08006b1d   Thumb Code    38  driver_ssd1306.o(i.ssd1306_set_entire_display)
    ssd1306_set_fade_blinking_mode           0x08006b45   Thumb Code    62  driver_ssd1306.o(i.ssd1306_set_fade_blinking_mode)
    ssd1306_set_high_column_start_address    0x08006ba5   Thumb Code    54  driver_ssd1306.o(i.ssd1306_set_high_column_start_address)
    ssd1306_set_interface                    0x08006bf9   Thumb Code    14  driver_ssd1306.o(i.ssd1306_set_interface)
    ssd1306_set_low_column_start_address     0x08006c09   Thumb Code    50  driver_ssd1306.o(i.ssd1306_set_low_column_start_address)
    ssd1306_set_memory_addressing_mode       0x08006c59   Thumb Code    44  driver_ssd1306.o(i.ssd1306_set_memory_addressing_mode)
    ssd1306_set_multiplex_ratio              0x08006c85   Thumb Code    72  driver_ssd1306.o(i.ssd1306_set_multiplex_ratio)
    ssd1306_set_page_address_range           0x08006d15   Thumb Code    84  driver_ssd1306.o(i.ssd1306_set_page_address_range)
    ssd1306_set_precharge_period             0x08006dad   Thumb Code    76  driver_ssd1306.o(i.ssd1306_set_precharge_period)
    ssd1306_set_scan_direction               0x08006e41   Thumb Code    38  driver_ssd1306.o(i.ssd1306_set_scan_direction)
    ssd1306_set_segment_remap                0x08006e67   Thumb Code    38  driver_ssd1306.o(i.ssd1306_set_segment_remap)
    ssd1306_set_zoom_in                      0x08006e8d   Thumb Code    44  driver_ssd1306.o(i.ssd1306_set_zoom_in)
    usart_task                               0x08006eb9   Thumb Code   222  app_uasrt.o(i.usart_task)
    user_task                                0x08006fbd   Thumb Code    60  app_uasrt.o(i.user_task)
    wire_clock_deinit                        0x08007001   Thumb Code    14  wire.o(i.wire_clock_deinit)
    wire_clock_init                          0x08007015   Thumb Code    50  wire.o(i.wire_clock_init)
    wire_clock_write                         0x08007051   Thumb Code    32  wire.o(i.wire_clock_write)
    wire_deinit                              0x08007075   Thumb Code    14  wire.o(i.wire_deinit)
    wire_init                                0x08007089   Thumb Code    86  wire.o(i.wire_init)
    wire_write                               0x080070ed   Thumb Code    28  wire.o(i.wire_write)
    AHBPrescTable                            0x08007118   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08007128   Data           8  system_stm32f4xx.o(.constdata)
    __ctype_categories                       0x080088f0   Data          64  ctype_c.o(.constdata)
    Region$$Table$$Base                      0x08008930   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08008950   Number         0  anon$$obj.o(Region$$Table)
    count                                    0x20000000   Data           4  main.o(.data)
    uwTickFreq                               0x20000004   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000008   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x2000000c   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x20000010   Data           4  system_stm32f4xx.o(.data)
    x_angle_limit_flag                       0x20000020   Data           1  app_uasrt.o(.data)
    y_angle_limit_flag                       0x20000021   Data           1  app_uasrt.o(.data)
    motor_angle_limit_check_enabled          0x20000022   Data           1  app_uasrt.o(.data)
    x_reference_initialized                  0x20000023   Data           1  app_uasrt.o(.data)
    y_reference_initialized                  0x20000024   Data           1  app_uasrt.o(.data)
    x_initial_direction                      0x20000025   Data           1  app_uasrt.o(.data)
    y_initial_direction                      0x20000026   Data           1  app_uasrt.o(.data)
    initial_position_saved                   0x20000027   Data           1  app_uasrt.o(.data)
    x_motor_angle                            0x20000028   Data           4  app_uasrt.o(.data)
    y_motor_angle                            0x2000002c   Data           4  app_uasrt.o(.data)
    x_reference_position                     0x20000030   Data           4  app_uasrt.o(.data)
    y_reference_position                     0x20000034   Data           4  app_uasrt.o(.data)
    x_relative_angle                         0x20000038   Data           4  app_uasrt.o(.data)
    y_relative_angle                         0x2000003c   Data           4  app_uasrt.o(.data)
    x_initial_position                       0x20000040   Data           4  app_uasrt.o(.data)
    y_initial_position                       0x20000044   Data           4  app_uasrt.o(.data)
    motor_x                                  0x2000004d   Data           1  app_pid.o(.data)
    motor_y                                  0x2000004e   Data           1  app_pid.o(.data)
    target_x                                 0x20000050   Data           4  app_pid.o(.data)
    target_y                                 0x20000054   Data           4  app_pid.o(.data)
    current_x                                0x20000058   Data           4  app_pid.o(.data)
    current_y                                0x2000005c   Data           4  app_pid.o(.data)
    pid_params_x                             0x20000060   Data          36  app_pid.o(.data)
    pid_params_y                             0x20000084   Data          36  app_pid.o(.data)
    mt_oled                                  0x200000a8   Data          24  main.o(.bss)
    buffer                                   0x200000c0   Data          20  main.o(.bss)
    mt_usart                                 0x200000d8   Data          24  main.o(.bss)
    mt_cam                                   0x200000f0   Data          24  main.o(.bss)
    mt_user                                  0x20000108   Data          24  main.o(.bss)
    g_spi_handle                             0x20000120   Data          88  spi.o(.bss)
    motor_x_buf                              0x20000178   Data          64  usart.o(.bss)
    motor_y_buf                              0x200001b8   Data          64  usart.o(.bss)
    cam_rx_buf                               0x200001f8   Data          64  usart.o(.bss)
    user_rx_buf                              0x20000238   Data          64  usart.o(.bss)
    huart5                                   0x20000278   Data          72  usart.o(.bss)
    huart1                                   0x200002c0   Data          72  usart.o(.bss)
    huart2                                   0x20000308   Data          72  usart.o(.bss)
    huart3                                   0x20000350   Data          72  usart.o(.bss)
    hdma_uart5_rx                            0x20000398   Data          96  usart.o(.bss)
    hdma_usart1_rx                           0x200003f8   Data          96  usart.o(.bss)
    hdma_usart2_rx                           0x20000458   Data          96  usart.o(.bss)
    hdma_usart3_rx                           0x200004b8   Data          96  usart.o(.bss)
    ringbuffer_pool_x                        0x20000954   Data          64  app_uasrt.o(.bss)
    ringbuffer_pool_y                        0x20000994   Data          64  app_uasrt.o(.bss)
    ringbuffer_pool_cam                      0x200009d4   Data          64  app_uasrt.o(.bss)
    ringbuffer_pool_user                     0x20000a14   Data          64  app_uasrt.o(.bss)
    output_buffer_x                          0x20000a54   Data          64  app_uasrt.o(.bss)
    output_buffer_y                          0x20000a94   Data          64  app_uasrt.o(.bss)
    output_buffer_user                       0x20000ad4   Data          64  app_uasrt.o(.bss)
    ringbuffer_x                             0x20000b14   Data          12  app_uasrt.o(.bss)
    ringbuffer_y                             0x20000b20   Data          12  app_uasrt.o(.bss)
    ringbuffer_user                          0x20000b2c   Data          12  app_uasrt.o(.bss)
    output_buffer_cam                        0x20000b38   Data          64  app_uasrt.o(.bss)
    ringbuffer_cam                           0x20000b78   Data          12  app_uasrt.o(.bss)
    pid_x                                    0x20000b88   Data          60  app_pid.o(.bss)
    pid_y                                    0x20000bc4   Data          60  app_pid.o(.bss)
    __initial_sp                             0x20001030   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000089f8, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00008950, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         3953  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         4237    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         4240    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         4242    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         4244    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         4245    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         4252    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         4247    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         4249    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0800019c   0x0800019c   0x00000004   Code   RO         4238    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a0   0x080001a0   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x080001c4   0x080001c4   0x00000062   Code   RO         3956    .text               mc_w.l(uldiv.o)
    0x08000226   0x08000226   0x00000024   Code   RO         3960    .text               mc_w.l(memcpya.o)
    0x0800024a   0x0800024a   0x00000024   Code   RO         3962    .text               mc_w.l(memseta.o)
    0x0800026e   0x0800026e   0x0000000e   Code   RO         3966    .text               mc_w.l(strlen.o)
    0x0800027c   0x0800027c   0x0000001e   Code   RO         3968    .text               mc_w.l(strncmp.o)
    0x0800029a   0x0800029a   0x00000002   PAD
    0x0800029c   0x0800029c   0x00000038   Code   RO         4233    .text               mc_w.l(__0sscanf.o)
    0x080002d4   0x080002d4   0x0000014c   Code   RO         4235    .text               mc_w.l(_scanf_int.o)
    0x08000420   0x08000420   0x0000002c   Code   RO         4254    .text               mc_w.l(uidiv.o)
    0x0800044c   0x0800044c   0x0000001e   Code   RO         4256    .text               mc_w.l(llshl.o)
    0x0800046a   0x0800046a   0x00000020   Code   RO         4258    .text               mc_w.l(llushr.o)
    0x0800048a   0x0800048a   0x0000001c   Code   RO         4260    .text               mc_w.l(_chval.o)
    0x080004a6   0x080004a6   0x00000002   PAD
    0x080004a8   0x080004a8   0x00000028   Code   RO         4262    .text               mc_w.l(scanf_char.o)
    0x080004d0   0x080004d0   0x00000040   Code   RO         4264    .text               mc_w.l(_sgetc.o)
    0x08000510   0x08000510   0x00000000   Code   RO         4266    .text               mc_w.l(iusefp.o)
    0x08000510   0x08000510   0x0000014e   Code   RO         4267    .text               mf_w.l(dadd.o)
    0x0800065e   0x0800065e   0x000000e4   Code   RO         4269    .text               mf_w.l(dmul.o)
    0x08000742   0x08000742   0x000000de   Code   RO         4271    .text               mf_w.l(ddiv.o)
    0x08000820   0x08000820   0x00000030   Code   RO         4273    .text               mf_w.l(dfixul.o)
    0x08000850   0x08000850   0x00000030   Code   RO         4275    .text               mf_w.l(cdrcmple.o)
    0x08000880   0x08000880   0x00000024   Code   RO         4277    .text               mc_w.l(init.o)
    0x080008a4   0x080008a4   0x00000024   Code   RO         4279    .text               mc_w.l(llsshr.o)
    0x080008c8   0x080008c8   0x0000000a   Code   RO         4281    .text               mc_w.l(isspace_c.o)
    0x080008d2   0x080008d2   0x00000002   PAD
    0x080008d4   0x080008d4   0x00000330   Code   RO         4283    .text               mc_w.l(_scanf.o)
    0x08000c04   0x08000c04   0x000000ba   Code   RO         4285    .text               mf_w.l(depilogue.o)
    0x08000cbe   0x08000cbe   0x00000002   PAD
    0x08000cc0   0x08000cc0   0x00000028   Code   RO         4287    .text               mc_w.l(ctype_c.o)
    0x08000ce8   0x08000ce8   0x00000002   Code   RO          582    i.BusFault_Handler  stm32f4xx_it.o
    0x08000cea   0x08000cea   0x00000002   PAD
    0x08000cec   0x08000cec   0x0000000c   Code   RO          583    i.DMA1_Stream0_IRQHandler  stm32f4xx_it.o
    0x08000cf8   0x08000cf8   0x0000000c   Code   RO          584    i.DMA1_Stream1_IRQHandler  stm32f4xx_it.o
    0x08000d04   0x08000d04   0x0000000c   Code   RO          585    i.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x08000d10   0x08000d10   0x0000000c   Code   RO          586    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x08000d1c   0x08000d1c   0x00000002   Code   RO          587    i.DebugMon_Handler  stm32f4xx_it.o
    0x08000d1e   0x08000d1e   0x00000046   Code   RO         3268    i.Emm_V5_En_Control  emm_v5.o
    0x08000d64   0x08000d64   0x00000286   Code   RO         3274    i.Emm_V5_Parse_Response  emm_v5.o
    0x08000fea   0x08000fea   0x0000006c   Code   RO         3275    i.Emm_V5_Pos_Control  emm_v5.o
    0x08001056   0x08001056   0x000000da   Code   RO         3276    i.Emm_V5_Read_Sys_Params  emm_v5.o
    0x08001130   0x08001130   0x00000034   Code   RO         3278    i.Emm_V5_Reset_CurPos_To_Zero  emm_v5.o
    0x08001164   0x08001164   0x00000038   Code   RO         3279    i.Emm_V5_Stop_Now   emm_v5.o
    0x0800119c   0x0800119c   0x00000052   Code   RO         3281    i.Emm_V5_Vel_Control  emm_v5.o
    0x080011ee   0x080011ee   0x00000004   Code   RO          241    i.Error_Handler     main.o
    0x080011f2   0x080011f2   0x000000a6   Code   RO         1479    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08001298   0x08001298   0x00000024   Code   RO         1480    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x080012bc   0x080012bc   0x000001dc   Code   RO         1484    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08001498   0x08001498   0x0000015c   Code   RO         1485    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x080015f4   0x080015f4   0x00000092   Code   RO         1489    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08001686   0x08001686   0x00000002   PAD
    0x08001688   0x08001688   0x00000028   Code   RO         1899    i.HAL_Delay         stm32f4xx_hal.o
    0x080016b0   0x080016b0   0x00000150   Code   RO         1377    i.HAL_GPIO_DeInit   stm32f4xx_hal_gpio.o
    0x08001800   0x08001800   0x00000200   Code   RO         1380    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08001a00   0x08001a00   0x0000000a   Code   RO         1384    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08001a0a   0x08001a0a   0x00000002   PAD
    0x08001a0c   0x08001a0c   0x0000000c   Code   RO         1905    i.HAL_GetTick       stm32f4xx_hal.o
    0x08001a18   0x08001a18   0x00000010   Code   RO         1911    i.HAL_IncTick       stm32f4xx_hal.o
    0x08001a28   0x08001a28   0x00000034   Code   RO         1912    i.HAL_Init          stm32f4xx_hal.o
    0x08001a5c   0x08001a5c   0x00000040   Code   RO         1913    i.HAL_InitTick      stm32f4xx_hal.o
    0x08001a9c   0x08001a9c   0x00000034   Code   RO          710    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08001ad0   0x08001ad0   0x0000001e   Code   RO         1752    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08001aee   0x08001aee   0x00000002   PAD
    0x08001af0   0x08001af0   0x00000064   Code   RO         1758    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08001b54   0x08001b54   0x00000024   Code   RO         1759    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08001b78   0x08001b78   0x0000015c   Code   RO         1058    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08001cd4   0x08001cd4   0x00000020   Code   RO         1065    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08001cf4   0x08001cf4   0x00000020   Code   RO         1066    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08001d14   0x08001d14   0x00000074   Code   RO         1067    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08001d88   0x08001d88   0x00000404   Code   RO         1070    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x0800218c   0x0800218c   0x00000030   Code   RO          752    i.HAL_SPI_DeInit    stm32f4xx_hal_spi.o
    0x080021bc   0x080021bc   0x000000c2   Code   RO          757    i.HAL_SPI_Init      stm32f4xx_hal_spi.o
    0x0800227e   0x0800227e   0x00000002   PAD
    0x08002280   0x08002280   0x00000028   Code   RO          711    i.HAL_SPI_MspDeInit  stm32f4xx_hal_msp.o
    0x080022a8   0x080022a8   0x00000064   Code   RO          712    i.HAL_SPI_MspInit   stm32f4xx_hal_msp.o
    0x0800230c   0x0800230c   0x000001a0   Code   RO          765    i.HAL_SPI_Transmit  stm32f4xx_hal_spi.o
    0x080024ac   0x080024ac   0x00000016   Code   RO         1761    i.HAL_SYSTICK_CLKSourceConfig  stm32f4xx_hal_cortex.o
    0x080024c2   0x080024c2   0x00000002   PAD
    0x080024c4   0x080024c4   0x0000002c   Code   RO         1763    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x080024f0   0x080024f0   0x0000005e   Code   RO         2160    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x0800254e   0x0800254e   0x00000002   PAD
    0x08002550   0x08002550   0x000001c0   Code   RO          512    i.HAL_UARTEx_RxEventCallback  usart.o
    0x08002710   0x08002710   0x00000002   Code   RO         2176    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08002712   0x08002712   0x00000002   PAD
    0x08002714   0x08002714   0x00000308   Code   RO         2179    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08002a1c   0x08002a1c   0x00000064   Code   RO         2180    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08002a80   0x08002a80   0x0000027c   Code   RO          514    i.HAL_UART_MspInit  usart.o
    0x08002cfc   0x08002cfc   0x00000002   Code   RO         2186    i.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x08002cfe   0x08002cfe   0x00000002   Code   RO         2187    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08002d00   0x08002d00   0x00000152   Code   RO         2188    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08002e52   0x08002e52   0x00000002   Code   RO         2191    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08002e54   0x08002e54   0x00000002   Code   RO          588    i.HardFault_Handler  stm32f4xx_it.o
    0x08002e56   0x08002e56   0x00000002   PAD
    0x08002e58   0x08002e58   0x0000006c   Code   RO          401    i.MX_DMA_Init       dma.o
    0x08002ec4   0x08002ec4   0x000000d8   Code   RO          377    i.MX_GPIO_Init      gpio.o
    0x08002f9c   0x08002f9c   0x00000002   Code   RO          425    i.MX_SPI1_Init      spi.o
    0x08002f9e   0x08002f9e   0x00000002   PAD
    0x08002fa0   0x08002fa0   0x00000054   Code   RO          515    i.MX_UART5_Init     usart.o
    0x08002ff4   0x08002ff4   0x00000054   Code   RO          516    i.MX_USART1_UART_Init  usart.o
    0x08003048   0x08003048   0x00000054   Code   RO          517    i.MX_USART2_UART_Init  usart.o
    0x0800309c   0x0800309c   0x00000054   Code   RO          518    i.MX_USART3_UART_Init  usart.o
    0x080030f0   0x080030f0   0x00000002   Code   RO          589    i.MemManage_Handler  stm32f4xx_it.o
    0x080030f2   0x080030f2   0x00000002   PAD
    0x080030f4   0x080030f4   0x0000003c   Code   RO         3542    i.Motor_Init        app_motor.o
    0x08003130   0x08003130   0x0000009c   Code   RO         3543    i.Motor_Set_Speed   app_motor.o
    0x080031cc   0x080031cc   0x00000024   Code   RO         3544    i.Motor_Stop        app_motor.o
    0x080031f0   0x080031f0   0x00000002   Code   RO          590    i.NMI_Handler       stm32f4xx_it.o
    0x080031f2   0x080031f2   0x00000002   Code   RO          591    i.PendSV_Handler    stm32f4xx_it.o
    0x080031f4   0x080031f4   0x0000006c   Code   RO          793    i.SPI_EndRxTxTransaction  stm32f4xx_hal_spi.o
    0x08003260   0x08003260   0x000000d0   Code   RO          798    i.SPI_WaitFlagStateUntilTimeout  stm32f4xx_hal_spi.o
    0x08003330   0x08003330   0x00000002   Code   RO          592    i.SVC_Handler       stm32f4xx_it.o
    0x08003332   0x08003332   0x00000004   Code   RO          593    i.SysTick_Handler   stm32f4xx_it.o
    0x08003336   0x08003336   0x00000002   PAD
    0x08003338   0x08003338   0x00000094   Code   RO          242    i.SystemClock_Config  main.o
    0x080033cc   0x080033cc   0x00000010   Code   RO         2503    i.SystemInit        system_stm32f4xx.o
    0x080033dc   0x080033dc   0x0000002c   Code   RO          594    i.UART5_IRQHandler  stm32f4xx_it.o
    0x08003408   0x08003408   0x00000010   Code   RO         2193    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08003418   0x08003418   0x000000ae   Code   RO         2194    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x080034c6   0x080034c6   0x00000098   Code   RO         2195    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x0800355e   0x0800355e   0x0000001e   Code   RO         2197    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x0800357c   0x0800357c   0x000000ce   Code   RO         2203    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x0800364a   0x0800364a   0x00000002   PAD
    0x0800364c   0x0800364c   0x000000f0   Code   RO         2204    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x0800373c   0x0800373c   0x000000a0   Code   RO         2205    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x080037dc   0x080037dc   0x000000ba   Code   RO         2207    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08003896   0x08003896   0x00000002   PAD
    0x08003898   0x08003898   0x0000002c   Code   RO          595    i.USART1_IRQHandler  stm32f4xx_it.o
    0x080038c4   0x080038c4   0x0000002c   Code   RO          596    i.USART2_IRQHandler  stm32f4xx_it.o
    0x080038f0   0x080038f0   0x0000002c   Code   RO          597    i.USART3_IRQHandler  stm32f4xx_it.o
    0x0800391c   0x0800391c   0x00000002   Code   RO          598    i.UsageFault_Handler  stm32f4xx_it.o
    0x0800391e   0x0800391e   0x00000002   PAD
    0x08003920   0x08003920   0x00000028   Code   RO         4207    i.__0sprintf        mc_w.l(printfa.o)
    0x08003948   0x08003948   0x00000034   Code   RO         4210    i.__0vsnprintf      mc_w.l(printfa.o)
    0x0800397c   0x0800397c   0x0000000e   Code   RO         4292    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800398a   0x0800398a   0x00000002   Code   RO         4293    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800398c   0x0800398c   0x0000000e   Code   RO         4294    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800399a   0x0800399a   0x00000002   PAD
    0x0800399c   0x0800399c   0x00000184   Code   RO         4212    i._fp_digits        mc_w.l(printfa.o)
    0x08003b20   0x08003b20   0x000006b4   Code   RO         4213    i._printf_core      mc_w.l(printfa.o)
    0x080041d4   0x080041d4   0x00000024   Code   RO         4214    i._printf_post_padding  mc_w.l(printfa.o)
    0x080041f8   0x080041f8   0x0000002e   Code   RO         4215    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08004226   0x08004226   0x00000016   Code   RO         4216    i._snputc           mc_w.l(printfa.o)
    0x0800423c   0x0800423c   0x0000000a   Code   RO         4217    i._sputc            mc_w.l(printfa.o)
    0x08004246   0x08004246   0x00000002   PAD
    0x08004248   0x08004248   0x00000060   Code   RO         2579    i.a_iic_send_byte   iic.o
    0x080042a8   0x080042a8   0x00000040   Code   RO         2580    i.a_iic_start       iic.o
    0x080042e8   0x080042e8   0x00000048   Code   RO         2581    i.a_iic_stop        iic.o
    0x08004330   0x08004330   0x00000054   Code   RO         2582    i.a_iic_wait_ack    iic.o
    0x08004384   0x08004384   0x00000052   Code   RO         2732    i.a_ssd1306_multiple_write_byte  driver_ssd1306.o
    0x080043d6   0x080043d6   0x00000058   Code   RO         2733    i.a_ssd1306_write_byte  driver_ssd1306.o
    0x0800442e   0x0800442e   0x00000002   PAD
    0x08004430   0x08004430   0x00000154   Code   RO         3852    i.app_pid_calc      app_pid.o
    0x08004584   0x08004584   0x00000074   Code   RO         3853    i.app_pid_init      app_pid.o
    0x080045f8   0x080045f8   0x00000274   Code   RO         3854    i.app_pid_parse_cmd  app_pid.o
    0x0800486c   0x0800486c   0x00000074   Code   RO         3856    i.app_pid_report    app_pid.o
    0x080048e0   0x080048e0   0x00000074   Code   RO         3857    i.app_pid_report_task  app_pid.o
    0x08004954   0x08004954   0x00000034   Code   RO         3858    i.app_pid_set_target  app_pid.o
    0x08004988   0x08004988   0x00000088   Code   RO         3861    i.app_pid_start     app_pid.o
    0x08004a10   0x08004a10   0x00000050   Code   RO         3863    i.app_pid_task      app_pid.o
    0x08004a60   0x08004a60   0x0000000c   Code   RO          243    i.bsp_get_systick   main.o
    0x08004a6c   0x08004a6c   0x000000a0   Code   RO         3676    i.default_laser_callback  app_maixcam.o
    0x08004b0c   0x08004b0c   0x00000018   Code   RO         2540    i.delay_init        delay.o
    0x08004b24   0x08004b24   0x00000004   Code   RO         2541    i.delay_ms          delay.o
    0x08004b28   0x08004b28   0x00000034   Code   RO         2542    i.delay_us          delay.o
    0x08004b5c   0x08004b5c   0x00000014   Code   RO         2583    i.iic_deinit        iic.o
    0x08004b70   0x08004b70   0x0000004c   Code   RO         2584    i.iic_init          iic.o
    0x08004bbc   0x08004bbc   0x00000056   Code   RO         2588    i.iic_write         iic.o
    0x08004c12   0x08004c12   0x00000002   PAD
    0x08004c14   0x08004c14   0x000000d0   Code   RO          245    i.main              main.o
    0x08004ce4   0x08004ce4   0x0000009c   Code   RO         3677    i.maixcam_parse_data  app_maixcam.o
    0x08004d80   0x08004d80   0x00000014   Code   RO         3678    i.maixcam_set_callback  app_maixcam.o
    0x08004d94   0x08004d94   0x000000ac   Code   RO         3679    i.maixcam_task      app_maixcam.o
    0x08004e40   0x08004e40   0x00000018   Code   RO         3230    i.multiTimerInstall  multitimer.o
    0x08004e58   0x08004e58   0x00000070   Code   RO         3231    i.multiTimerStart   multitimer.o
    0x08004ec8   0x08004ec8   0x00000024   Code   RO         3232    i.multiTimerStop    multitimer.o
    0x08004eec   0x08004eec   0x00000044   Code   RO         3233    i.multiTimerYield   multitimer.o
    0x08004f30   0x08004f30   0x00000032   Code   RO         3581    i.my_printf         app_uasrt.o
    0x08004f62   0x08004f62   0x00000002   PAD
    0x08004f64   0x08004f64   0x00000068   Code   RO          246    i.oled_task         main.o
    0x08004fcc   0x08004fcc   0x000000f4   Code   RO         3582    i.parse_x_motor_data  app_uasrt.o
    0x080050c0   0x080050c0   0x000000d8   Code   RO         3583    i.parse_y_motor_data  app_uasrt.o
    0x08005198   0x08005198   0x00000088   Code   RO         3371    i.pid_calculate_positional  pid.o
    0x08005220   0x08005220   0x00000030   Code   RO         3372    i.pid_init          pid.o
    0x08005250   0x08005250   0x0000005c   Code   RO         3865    i.pid_laser_coord_callback  app_pid.o
    0x080052ac   0x080052ac   0x00000028   Code   RO         3373    i.pid_reset         pid.o
    0x080052d4   0x080052d4   0x00000006   Code   RO         3374    i.pid_set_limit     pid.o
    0x080052da   0x080052da   0x0000000e   Code   RO         3375    i.pid_set_params    pid.o
    0x080052e8   0x080052e8   0x00000006   Code   RO         3376    i.pid_set_target    pid.o
    0x080052ee   0x080052ee   0x00000002   PAD
    0x080052f0   0x080052f0   0x0000012c   Code   RO         3584    i.process_command   app_uasrt.o
    0x0800541c   0x0800541c   0x000000c4   Code   RO         3585    i.process_reset_command  app_uasrt.o
    0x080054e0   0x080054e0   0x00000030   Code   RO         3157    i.rt_ringbuffer_data_len  ringbuffer.o
    0x08005510   0x08005510   0x0000007e   Code   RO         3158    i.rt_ringbuffer_get  ringbuffer.o
    0x0800558e   0x0800558e   0x00000034   Code   RO         3160    i.rt_ringbuffer_init  ringbuffer.o
    0x080055c2   0x080055c2   0x00000082   Code   RO         3162    i.rt_ringbuffer_put  ringbuffer.o
    0x08005644   0x08005644   0x00000070   Code   RO         3586    i.save_initial_position  app_uasrt.o
    0x080056b4   0x080056b4   0x00000020   Code   RO          426    i.spi_deinit        spi.o
    0x080056d4   0x080056d4   0x00000098   Code   RO          427    i.spi_init          spi.o
    0x0800576c   0x0800576c   0x00000048   Code   RO          434    i.spi_write_cmd     spi.o
    0x080057b4   0x080057b4   0x00000014   Code   RO         3425    i.ssd1306_basic_clear  app_oled.o
    0x080057c8   0x080057c8   0x00000718   Code   RO         3429    i.ssd1306_basic_init  app_oled.o
    0x08005ee0   0x08005ee0   0x00000038   Code   RO         3433    i.ssd1306_basic_string  app_oled.o
    0x08005f18   0x08005f18   0x000000a8   Code   RO         2735    i.ssd1306_clear     driver_ssd1306.o
    0x08005fc0   0x08005fc0   0x0000001c   Code   RO         2736    i.ssd1306_deactivate_scroll  driver_ssd1306.o
    0x08005fdc   0x08005fdc   0x00000174   Code   RO         2737    i.ssd1306_deinit    driver_ssd1306.o
    0x08006150   0x08006150   0x000000a0   Code   RO         2743    i.ssd1306_gram_update  driver_ssd1306.o
    0x080061f0   0x080061f0   0x000001e4   Code   RO         2745    i.ssd1306_gram_write_string  driver_ssd1306.o
    0x080063d4   0x080063d4   0x0000040c   Code   RO         2747    i.ssd1306_init      driver_ssd1306.o
    0x080067e0   0x080067e0   0x00000002   Code   RO         3048    i.ssd1306_interface_debug_print  stm32f407_driver_ssd1306_interface.o
    0x080067e2   0x080067e2   0x00000004   Code   RO         3049    i.ssd1306_interface_delay_ms  stm32f407_driver_ssd1306_interface.o
    0x080067e6   0x080067e6   0x00000004   Code   RO         3050    i.ssd1306_interface_iic_deinit  stm32f407_driver_ssd1306_interface.o
    0x080067ea   0x080067ea   0x00000004   Code   RO         3051    i.ssd1306_interface_iic_init  stm32f407_driver_ssd1306_interface.o
    0x080067ee   0x080067ee   0x00000004   Code   RO         3052    i.ssd1306_interface_iic_write  stm32f407_driver_ssd1306_interface.o
    0x080067f2   0x080067f2   0x00000004   Code   RO         3053    i.ssd1306_interface_reset_gpio_deinit  stm32f407_driver_ssd1306_interface.o
    0x080067f6   0x080067f6   0x00000004   Code   RO         3054    i.ssd1306_interface_reset_gpio_init  stm32f407_driver_ssd1306_interface.o
    0x080067fa   0x080067fa   0x00000004   Code   RO         3055    i.ssd1306_interface_reset_gpio_write  stm32f407_driver_ssd1306_interface.o
    0x080067fe   0x080067fe   0x00000004   Code   RO         3056    i.ssd1306_interface_spi_cmd_data_gpio_deinit  stm32f407_driver_ssd1306_interface.o
    0x08006802   0x08006802   0x00000004   Code   RO         3057    i.ssd1306_interface_spi_cmd_data_gpio_init  stm32f407_driver_ssd1306_interface.o
    0x08006806   0x08006806   0x00000004   Code   RO         3058    i.ssd1306_interface_spi_cmd_data_gpio_write  stm32f407_driver_ssd1306_interface.o
    0x0800680a   0x0800680a   0x00000004   Code   RO         3059    i.ssd1306_interface_spi_deinit  stm32f407_driver_ssd1306_interface.o
    0x0800680e   0x0800680e   0x00000006   Code   RO         3060    i.ssd1306_interface_spi_init  stm32f407_driver_ssd1306_interface.o
    0x08006814   0x08006814   0x00000004   Code   RO         3061    i.ssd1306_interface_spi_write_cmd  stm32f407_driver_ssd1306_interface.o
    0x08006818   0x08006818   0x0000000e   Code   RO         2749    i.ssd1306_set_addr_pin  driver_ssd1306.o
    0x08006826   0x08006826   0x00000032   Code   RO         2750    i.ssd1306_set_charge_pump  driver_ssd1306.o
    0x08006858   0x08006858   0x00000098   Code   RO         2751    i.ssd1306_set_column_address_range  driver_ssd1306.o
    0x080068f0   0x080068f0   0x00000036   Code   RO         2752    i.ssd1306_set_com_pins_hardware_conf  driver_ssd1306.o
    0x08006926   0x08006926   0x0000002c   Code   RO         2753    i.ssd1306_set_contrast  driver_ssd1306.o
    0x08006952   0x08006952   0x0000002e   Code   RO         2754    i.ssd1306_set_deselect_level  driver_ssd1306.o
    0x08006980   0x08006980   0x00000026   Code   RO         2755    i.ssd1306_set_display  driver_ssd1306.o
    0x080069a6   0x080069a6   0x00000002   PAD
    0x080069a8   0x080069a8   0x0000009c   Code   RO         2756    i.ssd1306_set_display_clock  driver_ssd1306.o
    0x08006a44   0x08006a44   0x00000026   Code   RO         2757    i.ssd1306_set_display_mode  driver_ssd1306.o
    0x08006a6a   0x08006a6a   0x00000002   PAD
    0x08006a6c   0x08006a6c   0x0000005c   Code   RO         2758    i.ssd1306_set_display_offset  driver_ssd1306.o
    0x08006ac8   0x08006ac8   0x00000054   Code   RO         2759    i.ssd1306_set_display_start_line  driver_ssd1306.o
    0x08006b1c   0x08006b1c   0x00000026   Code   RO         2760    i.ssd1306_set_entire_display  driver_ssd1306.o
    0x08006b42   0x08006b42   0x00000002   PAD
    0x08006b44   0x08006b44   0x00000060   Code   RO         2761    i.ssd1306_set_fade_blinking_mode  driver_ssd1306.o
    0x08006ba4   0x08006ba4   0x00000054   Code   RO         2762    i.ssd1306_set_high_column_start_address  driver_ssd1306.o
    0x08006bf8   0x08006bf8   0x0000000e   Code   RO         2763    i.ssd1306_set_interface  driver_ssd1306.o
    0x08006c06   0x08006c06   0x00000002   PAD
    0x08006c08   0x08006c08   0x00000050   Code   RO         2765    i.ssd1306_set_low_column_start_address  driver_ssd1306.o
    0x08006c58   0x08006c58   0x0000002c   Code   RO         2766    i.ssd1306_set_memory_addressing_mode  driver_ssd1306.o
    0x08006c84   0x08006c84   0x00000090   Code   RO         2767    i.ssd1306_set_multiplex_ratio  driver_ssd1306.o
    0x08006d14   0x08006d14   0x00000098   Code   RO         2769    i.ssd1306_set_page_address_range  driver_ssd1306.o
    0x08006dac   0x08006dac   0x00000094   Code   RO         2770    i.ssd1306_set_precharge_period  driver_ssd1306.o
    0x08006e40   0x08006e40   0x00000026   Code   RO         2772    i.ssd1306_set_scan_direction  driver_ssd1306.o
    0x08006e66   0x08006e66   0x00000026   Code   RO         2773    i.ssd1306_set_segment_remap  driver_ssd1306.o
    0x08006e8c   0x08006e8c   0x0000002c   Code   RO         2777    i.ssd1306_set_zoom_in  driver_ssd1306.o
    0x08006eb8   0x08006eb8   0x00000104   Code   RO         3587    i.usart_task        app_uasrt.o
    0x08006fbc   0x08006fbc   0x00000044   Code   RO         3588    i.user_task         app_uasrt.o
    0x08007000   0x08007000   0x00000014   Code   RO         2672    i.wire_clock_deinit  wire.o
    0x08007014   0x08007014   0x0000003c   Code   RO         2673    i.wire_clock_init   wire.o
    0x08007050   0x08007050   0x00000024   Code   RO         2674    i.wire_clock_write  wire.o
    0x08007074   0x08007074   0x00000014   Code   RO         2675    i.wire_deinit       wire.o
    0x08007088   0x08007088   0x00000064   Code   RO         2676    i.wire_init         wire.o
    0x080070ec   0x080070ec   0x00000024   Code   RO         2678    i.wire_write        wire.o
    0x08007110   0x08007110   0x00000008   Data   RO         1491    .constdata          stm32f4xx_hal_dma.o
    0x08007118   0x08007118   0x00000010   Data   RO         2504    .constdata          system_stm32f4xx.o
    0x08007128   0x08007128   0x00000008   Data   RO         2505    .constdata          system_stm32f4xx.o
    0x08007130   0x08007130   0x000017c0   Data   RO         2781    .constdata          driver_ssd1306.o
    0x080088f0   0x080088f0   0x00000040   Data   RO         4288    .constdata          mc_w.l(ctype_c.o)
    0x08008930   0x08008930   0x00000020   Data   RO         4290    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08008950, Size: 0x00001030, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08008950   0x00000004   Data   RW          253    .data               main.o
    0x20000004   0x08008954   0x0000000c   Data   RW         1919    .data               stm32f4xx_hal.o
    0x20000010   0x08008960   0x00000004   Data   RW         2506    .data               system_stm32f4xx.o
    0x20000014   0x08008964   0x00000004   Data   RW         2543    .data               delay.o
    0x20000018   0x08008968   0x00000008   Data   RW         3234    .data               multitimer.o
    0x20000020   0x08008970   0x00000028   Data   RW         3596    .data               app_uasrt.o
    0x20000048   0x08008998   0x00000004   Data   RW         3680    .data               app_maixcam.o
    0x2000004c   0x0800899c   0x0000005c   Data   RW         3867    .data               app_pid.o
    0x200000a8        -       0x0000002c   Zero   RW          248    .bss                main.o
    0x200000d4   0x080089f8   0x00000004   PAD
    0x200000d8        -       0x00000018   Zero   RW          249    .bss                main.o
    0x200000f0        -       0x00000018   Zero   RW          250    .bss                main.o
    0x20000108        -       0x00000018   Zero   RW          252    .bss                main.o
    0x20000120        -       0x00000058   Zero   RW          436    .bss                spi.o
    0x20000178        -       0x000003a0   Zero   RW          519    .bss                usart.o
    0x20000518        -       0x0000043c   Zero   RW         3435    .bss                app_oled.o
    0x20000954        -       0x00000040   Zero   RW         3589    .bss                app_uasrt.o
    0x20000994        -       0x00000040   Zero   RW         3590    .bss                app_uasrt.o
    0x200009d4        -       0x00000040   Zero   RW         3591    .bss                app_uasrt.o
    0x20000a14        -       0x00000040   Zero   RW         3592    .bss                app_uasrt.o
    0x20000a54        -       0x000000e4   Zero   RW         3593    .bss                app_uasrt.o
    0x20000b38        -       0x00000040   Zero   RW         3594    .bss                app_uasrt.o
    0x20000b78        -       0x0000000c   Zero   RW         3595    .bss                app_uasrt.o
    0x20000b84   0x080089f8   0x00000004   PAD
    0x20000b88        -       0x000000a8   Zero   RW         3866    .bss                app_pid.o
    0x20000c30        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x080089f8, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       508        144          0          4          0       4611   app_maixcam.o
       252         30          0          0          0       2176   app_motor.o
      1892       1066          0          0       1084       6683   app_oled.o
      1676        298          0         92        168       8309   app_pid.o
      1446        338          0         40        560       9651   app_uasrt.o
        80         12          0          4          0       2343   delay.o
       108          4          0          0          0        882   dma.o
      4106       1494       6080          0          0      36441   driver_ssd1306.o
      1232         60          0          0          0       7547   emm_v5.o
       216         16          0          0          0       1083   gpio.o
       498         52          0          0          0       5372   iic.o
       476         88          0          4        116      12668   main.o
       240         20          0          8          0       4361   multitimer.o
       250         12          0          0          0       4328   pid.o
       356          0          0          0          0       5098   ringbuffer.o
       258         34          0          0         88       3106   spi.o
        36          8        392          0       1024        864   startup_stm32f407xx.o
        56          0          0          0          0       9712   stm32f407_driver_ssd1306_interface.o
       184         30          0         12          0       9601   stm32f4xx_hal.o
       232         16          0          0          0      34141   stm32f4xx_hal_cortex.o
      1172         20          8          0          0       5978   stm32f4xx_hal_dma.o
       858         30          0          0          0       3102   stm32f4xx_hal_gpio.o
       192         30          0          0          0       2244   stm32f4xx_hal_msp.o
      1556        104          0          0          0       5592   stm32f4xx_hal_rcc.o
       974         14          0          0          0       5780   stm32f4xx_hal_spi.o
      2480         20          0          0          0      15407   stm32f4xx_hal_uart.o
       244         72          0          0          0       8505   stm32f4xx_it.o
        16          4         24          4          0       1199   system_stm32f4xx.o
         0          0          0          0          0     665012   uart.o
      1420        186          0          0        928       5414   usart.o
       272         48          0          0          0       4508   wire.o

    ----------------------------------------------------------------------
     23332       <USER>       <GROUP>        168       3976     891718   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        46          0          0          0          8          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        56          8          0          0          0         84   __0sscanf.o
        28          0          0          0          0         68   _chval.o
       816          6          0          0          0        112   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
        64          0          0          0          0         84   _sgetc.o
        40          6         64          0          0         68   ctype_c.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        10          0          0          0          0         68   isspace_c.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2310         92          0          0          0        680   printfa.o
        40          8          0          0          0         84   scanf_char.o
        14          0          0          0          0         68   strlen.o
        30          0          0          0          0         80   strncmp.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o

    ----------------------------------------------------------------------
      5220        <USER>         <GROUP>          0          0       2768   Library Totals
        12          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      4142        136         64          0          0       2112   mc_w.l
      1066          0          0          0          0        656   mf_w.l

    ----------------------------------------------------------------------
      5220        <USER>         <GROUP>          0          0       2768   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     28552       4386       6600        168       3976     874982   Grand Totals
     28552       4386       6600        168       3976     874982   ELF Image Totals
     28552       4386       6600        168          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                35152 (  34.33kB)
    Total RW  Size (RW Data + ZI Data)              4144 (   4.05kB)
    Total ROM Size (Code + RO Data + RW Data)      35320 (  34.49kB)

==============================================================================

